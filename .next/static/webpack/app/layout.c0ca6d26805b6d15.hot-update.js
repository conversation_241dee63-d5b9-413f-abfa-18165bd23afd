"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"3247f920a41b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NjVjZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMyNDdmOTIwYTQxYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/reader/credit-balance-widget.tsx":
/*!*********************************************************!*\
  !*** ./src/components/reader/credit-balance-widget.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreditBalanceWidget: function() { return /* binding */ CreditBalanceWidget; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _lib_credits__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/credits */ \"(app-pages-browser)/./src/lib/credits.ts\");\n/* harmony import */ var _components_credits_credit_purchase_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/credits/credit-purchase-modal */ \"(app-pages-browser)/./src/components/credits/credit-purchase-modal.tsx\");\n/* harmony import */ var _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/store/api/creditsApi */ \"(app-pages-browser)/./src/store/api/creditsApi.ts\");\n/* harmony import */ var _store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/store/slices/creditSlice */ \"(app-pages-browser)/./src/store/slices/creditSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ CreditBalanceWidget auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CreditBalanceWidget(param) {\n    let { className, showPurchaseButton = true, compact = false, showLowBalanceAlert = true } = param;\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useDispatch)();\n    const [showPurchaseModal, setShowPurchaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get current credit balance\n    const { data: balanceData, isLoading: isLoadingBalance, refetch: refetchBalance } = (0,_store_api_creditsApi__WEBPACK_IMPORTED_MODULE_10__.useGetCreditBalanceQuery)(undefined, {\n        skip: !(session === null || session === void 0 ? void 0 : session.user),\n        pollingInterval: 30000\n    });\n    var _balanceData_balance;\n    const currentBalance = (_balanceData_balance = balanceData === null || balanceData === void 0 ? void 0 : balanceData.balance) !== null && _balanceData_balance !== void 0 ? _balanceData_balance : 0;\n    const handleTopUpClick = ()=>{\n        console.log(\"Top up clicked! Session:\", session === null || session === void 0 ? void 0 : session.user);\n        console.log(\"Dispatching openPurchaseModal...\");\n        dispatch((0,_store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__.openPurchaseModal)(null));\n    };\n    // If user is not authenticated, show sign-in prompt\n    if (!(session === null || session === void 0 ? void 0 : session.user)) {\n        if (compact) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"border-blue-200 bg-blue-50 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                className: \"pb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center gap-2 text-blue-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this),\n                            \"Sign In Required\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                        className: \"text-blue-600\",\n                        children: \"Sign in to view your credit balance\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    if (compact) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 \".concat(className),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"h-4 w-4 text-yellow-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium\",\n                            children: \"Credits:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        isLoadingBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-5 w-12\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                            variant: currentBalance < 10 ? \"destructive\" : currentBalance < 50 ? \"secondary\" : \"default\",\n                            className: \"font-bold cursor-pointer hover:opacity-80 transition-opacity\",\n                            onClick: handleTopUpClick,\n                            title: \"Click to top up credits\",\n                            children: (0,_lib_credits__WEBPACK_IMPORTED_MODULE_8__.formatCredits)(currentBalance)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, this),\n                        showPurchaseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                currentBalance < 20 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleTopUpClick,\n                                    size: \"sm\",\n                                    variant: \"default\",\n                                    className: \"h-6 px-2 text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-3 w-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Top Up\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleTopUpClick,\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    className: \"h-6 px-2 text-xs\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_credits_credit_purchase_modal__WEBPACK_IMPORTED_MODULE_9__.CreditPurchaseModal, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: className,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        className: \"pb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-yellow-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Your Credits\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Use credits to unlock premium chapters\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Current Balance:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    isLoadingBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-6 w-16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: currentBalance < 10 ? \"destructive\" : currentBalance < 50 ? \"secondary\" : \"default\",\n                                                className: \"text-lg font-bold px-3 py-1\",\n                                                children: (0,_lib_credits__WEBPACK_IMPORTED_MODULE_8__.formatCredits)(currentBalance)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    \"($\",\n                                                    (0,_lib_credits__WEBPACK_IMPORTED_MODULE_8__.creditsToUSD)(currentBalance).toFixed(2),\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            showLowBalanceAlert && currentBalance < 20 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                className: \"border-orange-200 bg-orange-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                        className: \"text-orange-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium mb-1\",\n                                                children: \"Low Credit Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: \"Consider topping up to continue enjoying premium content without interruption.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            showPurchaseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-2 border-t space-y-2\",\n                                children: [\n                                    currentBalance < 50 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Value packs available with bonus credits\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleTopUpClick,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Buy More Credits\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_credits_credit_purchase_modal__WEBPACK_IMPORTED_MODULE_9__.CreditPurchaseModal, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CreditBalanceWidget, \"G1Or5UBw3f5TC85VBR364RKtz6c=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useDispatch,\n        _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_10__.useGetCreditBalanceQuery\n    ];\n});\n_c = CreditBalanceWidget;\nvar _c;\n$RefreshReg$(_c, \"CreditBalanceWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/reader/credit-balance-widget.tsx\n"));

/***/ })

});