"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"0478621394e9\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NjVjZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA0Nzg2MjEzOTRlOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/credits/mobile-credit-balance.tsx":
/*!**********************************************************!*\
  !*** ./src/components/credits/mobile-credit-balance.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileCreditBalance: function() { return /* binding */ MobileCreditBalance; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Plus,RefreshCw,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Plus,RefreshCw,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Plus,RefreshCw,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Plus,RefreshCw,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Plus,RefreshCw,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Plus,RefreshCw,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _lib_credits__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/credits */ \"(app-pages-browser)/./src/lib/credits.ts\");\n/* harmony import */ var _mobile_credit_purchase__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./mobile-credit-purchase */ \"(app-pages-browser)/./src/components/credits/mobile-credit-purchase.tsx\");\n/* harmony import */ var _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/store/api/creditsApi */ \"(app-pages-browser)/./src/store/api/creditsApi.ts\");\n/* harmony import */ var _store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/store/slices/creditSlice */ \"(app-pages-browser)/./src/store/slices/creditSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ MobileCreditBalance auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction MobileCreditBalance(param) {\n    let { compact = false, showPurchaseButton = true, className } = param;\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useDispatch)();\n    // Redux state\n    const balance = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector)(_store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__.selectCreditBalance);\n    const isLoading = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector)(_store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__.selectCreditLoading);\n    const shouldShowLowBalanceWarning = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector)(_store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__.selectShouldShowLowBalanceWarning);\n    // RTK Query\n    const { data: balanceData, isLoading: isQueryLoading, refetch } = (0,_store_api_creditsApi__WEBPACK_IMPORTED_MODULE_10__.useGetCreditBalanceQuery)(undefined, {\n        skip: !(session === null || session === void 0 ? void 0 : session.user),\n        pollingInterval: 60000\n    });\n    // Update Redux state when balance data changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if ((balanceData === null || balanceData === void 0 ? void 0 : balanceData.balance) !== undefined) {\n            dispatch((0,_store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__.setCreditBalance)(balanceData.balance));\n        }\n    }, [\n        balanceData,\n        dispatch\n    ]);\n    // Check for low balance warning\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (balance > 0) {\n            dispatch((0,_store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__.checkAndShowLowBalanceWarning)());\n        }\n    }, [\n        balance,\n        dispatch\n    ]);\n    const handleRefresh = async ()=>{\n        try {\n            await refetch();\n        } catch (error) {\n            console.error(\"Failed to refresh balance:\", error);\n        }\n    };\n    const handleDismissWarning = ()=>{\n        dispatch((0,_store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__.dismissLowBalanceWarning)());\n    };\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: className,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-5 w-5 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"Sign in to view credits\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this);\n    }\n    if (compact) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-4 w-4 text-yellow-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        isLoading || isQueryLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-5 w-12\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                            variant: \"secondary\",\n                            className: \"text-sm font-semibold cursor-pointer hover:opacity-80 transition-opacity\",\n                            onClick: ()=>{\n                                console.log(\"Mobile credit balance clicked!\");\n                                dispatch((0,_store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__.openPurchaseModal)(null));\n                            },\n                            title: \"Tap to top up credits\",\n                            children: (0,_lib_credits__WEBPACK_IMPORTED_MODULE_8__.formatCredits)(balance)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                showPurchaseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mobile_credit_purchase__WEBPACK_IMPORTED_MODULE_9__.MobileCreditPurchase, {\n                    trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        size: \"sm\",\n                        variant: \"outline\",\n                        className: \"h-7 px-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"h-3 w-3\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 15\n                    }, void 0),\n                    onSuccess: handleRefresh\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-3 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Credits\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: handleRefresh,\n                                    disabled: isLoading || isQueryLoading,\n                                    className: \"h-8 w-8 p-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 \".concat(isLoading || isQueryLoading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        isLoading || isQueryLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-8 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold cursor-pointer hover:opacity-80 transition-opacity\",\n                                            onClick: ()=>dispatch((0,_store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__.openPurchaseModal)(null)),\n                                            title: \"Tap to top up credits\",\n                                            children: balance.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Available credits\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                showPurchaseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mobile_credit_purchase__WEBPACK_IMPORTED_MODULE_9__.MobileCreditPurchase, {\n                                    trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        size: \"sm\",\n                                        className: \"gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            \"Buy More\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    onSuccess: handleRefresh\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 pt-3 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-green-600\",\n                                                children: [\n                                                    \"$\",\n                                                    (balance * 0.10).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"Credit Value\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-blue-600\",\n                                                children: Math.floor(balance / 5)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"Chapters Available\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            shouldShowLowBalanceWarning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                className: \"border-yellow-200 bg-yellow-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        className: \"h-4 w-4 text-yellow-600\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                        className: \"text-yellow-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium text-sm\",\n                                            children: \"Low Credit Balance\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs\",\n                                            children: \"Consider purchasing more credits to continue reading.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 ml-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mobile_credit_purchase__WEBPACK_IMPORTED_MODULE_9__.MobileCreditPurchase, {\n                                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"sm\",\n                                                variant: \"outline\",\n                                                className: \"h-7 text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    \"Top Up\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            onSuccess: ()=>{\n                                                handleRefresh();\n                                                handleDismissWarning();\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            size: \"sm\",\n                                            variant: \"ghost\",\n                                            onClick: handleDismissWarning,\n                                            className: \"h-7 w-7 p-0 text-yellow-600\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this),\n            balance > 0 && balance < 50 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"bg-blue-50 border-blue-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Plus_RefreshCw_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-blue-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium\",\n                                        children: \"Pro Tip\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Buy credits in bulk to get bonus credits and better value!\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/credits/mobile-credit-balance.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n_s(MobileCreditBalance, \"XAiETB7h0mLgPYQAuRHkUtA+k5U=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useDispatch,\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector,\n        _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_10__.useGetCreditBalanceQuery\n    ];\n});\n_c = MobileCreditBalance;\nvar _c;\n$RefreshReg$(_c, \"MobileCreditBalance\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/credits/mobile-credit-balance.tsx\n"));

/***/ })

});