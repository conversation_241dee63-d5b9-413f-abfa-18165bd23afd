"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"9d467be3141c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NjVjZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjlkNDY3YmUzMTQxY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/reader/credit-balance-widget.tsx":
/*!*********************************************************!*\
  !*** ./src/components/reader/credit-balance-widget.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreditBalanceWidget: function() { return /* binding */ CreditBalanceWidget; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _lib_credits__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/credits */ \"(app-pages-browser)/./src/lib/credits.ts\");\n/* harmony import */ var _components_credits_credit_purchase_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/credits/credit-purchase-modal */ \"(app-pages-browser)/./src/components/credits/credit-purchase-modal.tsx\");\n/* harmony import */ var _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/store/api/creditsApi */ \"(app-pages-browser)/./src/store/api/creditsApi.ts\");\n/* harmony import */ var _store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/store/slices/creditSlice */ \"(app-pages-browser)/./src/store/slices/creditSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ CreditBalanceWidget auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CreditBalanceWidget(param) {\n    let { className, showPurchaseButton = true, compact = false, showLowBalanceAlert = true } = param;\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useDispatch)();\n    const [showPurchaseModal, setShowPurchaseModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get current credit balance\n    const { data: balanceData, isLoading: isLoadingBalance, refetch: refetchBalance } = (0,_store_api_creditsApi__WEBPACK_IMPORTED_MODULE_10__.useGetCreditBalanceQuery)(undefined, {\n        skip: !(session === null || session === void 0 ? void 0 : session.user),\n        pollingInterval: 30000\n    });\n    var _balanceData_balance;\n    const currentBalance = (_balanceData_balance = balanceData === null || balanceData === void 0 ? void 0 : balanceData.balance) !== null && _balanceData_balance !== void 0 ? _balanceData_balance : 0;\n    const handleTopUpClick = ()=>{\n        console.log(\"Top up clicked! Session:\", session === null || session === void 0 ? void 0 : session.user);\n        console.log(\"Dispatching openPurchaseModal...\");\n        dispatch((0,_store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_11__.openPurchaseModal)(null));\n    };\n    // If user is not authenticated, show sign-in prompt\n    if (!(session === null || session === void 0 ? void 0 : session.user)) {\n        if (compact) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"border-blue-200 bg-blue-50 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                className: \"pb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center gap-2 text-blue-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this),\n                            \"Sign In Required\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                        className: \"text-blue-600\",\n                        children: \"Sign in to view your credit balance\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    if (compact) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: \"Credits:\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                isLoadingBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                    className: \"h-5 w-12\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: currentBalance < 10 ? \"destructive\" : currentBalance < 50 ? \"secondary\" : \"default\",\n                    className: \"font-bold cursor-pointer hover:opacity-80 transition-opacity\",\n                    onClick: handleTopUpClick,\n                    title: \"Click to top up credits\",\n                    children: (0,_lib_credits__WEBPACK_IMPORTED_MODULE_8__.formatCredits)(currentBalance)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this),\n                showPurchaseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-1\",\n                    children: [\n                        currentBalance < 20 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: handleTopUpClick,\n                            size: \"sm\",\n                            variant: \"default\",\n                            className: \"h-6 px-2 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-3 w-3 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, this),\n                                \"Top Up\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: handleTopUpClick,\n                            size: \"sm\",\n                            variant: \"outline\",\n                            className: \"h-6 px-2 text-xs\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: className,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        className: \"pb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-yellow-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Your Credits\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Use credits to unlock premium chapters\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Current Balance:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    isLoadingBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                        className: \"h-6 w-16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: currentBalance < 10 ? \"destructive\" : currentBalance < 50 ? \"secondary\" : \"default\",\n                                                className: \"text-lg font-bold px-3 py-1\",\n                                                children: (0,_lib_credits__WEBPACK_IMPORTED_MODULE_8__.formatCredits)(currentBalance)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    \"($\",\n                                                    (0,_lib_credits__WEBPACK_IMPORTED_MODULE_8__.creditsToUSD)(currentBalance).toFixed(2),\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            showLowBalanceAlert && currentBalance < 20 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                className: \"border-orange-200 bg-orange-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                        className: \"text-orange-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium mb-1\",\n                                                children: \"Low Credit Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: \"Consider topping up to continue enjoying premium content without interruption.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            showPurchaseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-2 border-t space-y-2\",\n                                children: [\n                                    currentBalance < 50 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Value packs available with bonus credits\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleTopUpClick,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Buy More Credits\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_credits_credit_purchase_modal__WEBPACK_IMPORTED_MODULE_9__.CreditPurchaseModal, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-balance-widget.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CreditBalanceWidget, \"G1Or5UBw3f5TC85VBR364RKtz6c=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useDispatch,\n        _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_10__.useGetCreditBalanceQuery\n    ];\n});\n_c = CreditBalanceWidget;\nvar _c;\n$RefreshReg$(_c, \"CreditBalanceWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/reader/credit-balance-widget.tsx\n"));

/***/ })

});