"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/htmlparser2";
exports.ids = ["vendor-chunks/htmlparser2"];
exports.modules = {

/***/ "(rsc)/./node_modules/htmlparser2/lib/esm/Parser.js":
/*!****************************************************!*\
  !*** ./node_modules/htmlparser2/lib/esm/Parser.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Tokenizer.js */ \"(rsc)/./node_modules/htmlparser2/lib/esm/Tokenizer.js\");\n/* harmony import */ var entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! entities/lib/decode.js */ \"(rsc)/./node_modules/entities/lib/esm/decode.js\");\n\n\nconst formTags = new Set([\n    \"input\",\n    \"option\",\n    \"optgroup\",\n    \"select\",\n    \"button\",\n    \"datalist\",\n    \"textarea\",\n]);\nconst pTag = new Set([\"p\"]);\nconst tableSectionTags = new Set([\"thead\", \"tbody\"]);\nconst ddtTags = new Set([\"dd\", \"dt\"]);\nconst rtpTags = new Set([\"rt\", \"rp\"]);\nconst openImpliesClose = new Map([\n    [\"tr\", new Set([\"tr\", \"th\", \"td\"])],\n    [\"th\", new Set([\"th\"])],\n    [\"td\", new Set([\"thead\", \"th\", \"td\"])],\n    [\"body\", new Set([\"head\", \"link\", \"script\"])],\n    [\"li\", new Set([\"li\"])],\n    [\"p\", pTag],\n    [\"h1\", pTag],\n    [\"h2\", pTag],\n    [\"h3\", pTag],\n    [\"h4\", pTag],\n    [\"h5\", pTag],\n    [\"h6\", pTag],\n    [\"select\", formTags],\n    [\"input\", formTags],\n    [\"output\", formTags],\n    [\"button\", formTags],\n    [\"datalist\", formTags],\n    [\"textarea\", formTags],\n    [\"option\", new Set([\"option\"])],\n    [\"optgroup\", new Set([\"optgroup\", \"option\"])],\n    [\"dd\", ddtTags],\n    [\"dt\", ddtTags],\n    [\"address\", pTag],\n    [\"article\", pTag],\n    [\"aside\", pTag],\n    [\"blockquote\", pTag],\n    [\"details\", pTag],\n    [\"div\", pTag],\n    [\"dl\", pTag],\n    [\"fieldset\", pTag],\n    [\"figcaption\", pTag],\n    [\"figure\", pTag],\n    [\"footer\", pTag],\n    [\"form\", pTag],\n    [\"header\", pTag],\n    [\"hr\", pTag],\n    [\"main\", pTag],\n    [\"nav\", pTag],\n    [\"ol\", pTag],\n    [\"pre\", pTag],\n    [\"section\", pTag],\n    [\"table\", pTag],\n    [\"ul\", pTag],\n    [\"rt\", rtpTags],\n    [\"rp\", rtpTags],\n    [\"tbody\", tableSectionTags],\n    [\"tfoot\", tableSectionTags],\n]);\nconst voidElements = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\nconst foreignContextElements = new Set([\"math\", \"svg\"]);\nconst htmlIntegrationElements = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignobject\",\n    \"desc\",\n    \"title\",\n]);\nconst reNameEnd = /\\s|\\//;\nclass Parser {\n    constructor(cbs, options = {}) {\n        var _a, _b, _c, _d, _e;\n        this.options = options;\n        /** The start index of the last event. */\n        this.startIndex = 0;\n        /** The end index of the last event. */\n        this.endIndex = 0;\n        /**\n         * Store the start index of the current open tag,\n         * so we can update the start index for attributes.\n         */\n        this.openTagStart = 0;\n        this.tagname = \"\";\n        this.attribname = \"\";\n        this.attribvalue = \"\";\n        this.attribs = null;\n        this.stack = [];\n        this.foreignContext = [];\n        this.buffers = [];\n        this.bufferOffset = 0;\n        /** The index of the last written buffer. Used when resuming after a `pause()`. */\n        this.writeIndex = 0;\n        /** Indicates whether the parser has finished running / `.end` has been called. */\n        this.ended = false;\n        this.cbs = cbs !== null && cbs !== void 0 ? cbs : {};\n        this.lowerCaseTagNames = (_a = options.lowerCaseTags) !== null && _a !== void 0 ? _a : !options.xmlMode;\n        this.lowerCaseAttributeNames =\n            (_b = options.lowerCaseAttributeNames) !== null && _b !== void 0 ? _b : !options.xmlMode;\n        this.tokenizer = new ((_c = options.Tokenizer) !== null && _c !== void 0 ? _c : _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.options, this);\n        (_e = (_d = this.cbs).onparserinit) === null || _e === void 0 ? void 0 : _e.call(_d, this);\n    }\n    // Tokenizer event handlers\n    /** @internal */\n    ontext(start, endIndex) {\n        var _a, _b;\n        const data = this.getSlice(start, endIndex);\n        this.endIndex = endIndex - 1;\n        (_b = (_a = this.cbs).ontext) === null || _b === void 0 ? void 0 : _b.call(_a, data);\n        this.startIndex = endIndex;\n    }\n    /** @internal */\n    ontextentity(cp) {\n        var _a, _b;\n        /*\n         * Entities can be emitted on the character, or directly after.\n         * We use the section start here to get accurate indices.\n         */\n        const index = this.tokenizer.getSectionStart();\n        this.endIndex = index - 1;\n        (_b = (_a = this.cbs).ontext) === null || _b === void 0 ? void 0 : _b.call(_a, (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(cp));\n        this.startIndex = index;\n    }\n    isVoidElement(name) {\n        return !this.options.xmlMode && voidElements.has(name);\n    }\n    /** @internal */\n    onopentagname(start, endIndex) {\n        this.endIndex = endIndex;\n        let name = this.getSlice(start, endIndex);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        this.emitOpenTag(name);\n    }\n    emitOpenTag(name) {\n        var _a, _b, _c, _d;\n        this.openTagStart = this.startIndex;\n        this.tagname = name;\n        const impliesClose = !this.options.xmlMode && openImpliesClose.get(name);\n        if (impliesClose) {\n            while (this.stack.length > 0 &&\n                impliesClose.has(this.stack[this.stack.length - 1])) {\n                const element = this.stack.pop();\n                (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, element, true);\n            }\n        }\n        if (!this.isVoidElement(name)) {\n            this.stack.push(name);\n            if (foreignContextElements.has(name)) {\n                this.foreignContext.push(true);\n            }\n            else if (htmlIntegrationElements.has(name)) {\n                this.foreignContext.push(false);\n            }\n        }\n        (_d = (_c = this.cbs).onopentagname) === null || _d === void 0 ? void 0 : _d.call(_c, name);\n        if (this.cbs.onopentag)\n            this.attribs = {};\n    }\n    endOpenTag(isImplied) {\n        var _a, _b;\n        this.startIndex = this.openTagStart;\n        if (this.attribs) {\n            (_b = (_a = this.cbs).onopentag) === null || _b === void 0 ? void 0 : _b.call(_a, this.tagname, this.attribs, isImplied);\n            this.attribs = null;\n        }\n        if (this.cbs.onclosetag && this.isVoidElement(this.tagname)) {\n            this.cbs.onclosetag(this.tagname, true);\n        }\n        this.tagname = \"\";\n    }\n    /** @internal */\n    onopentagend(endIndex) {\n        this.endIndex = endIndex;\n        this.endOpenTag(false);\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onclosetag(start, endIndex) {\n        var _a, _b, _c, _d, _e, _f;\n        this.endIndex = endIndex;\n        let name = this.getSlice(start, endIndex);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        if (foreignContextElements.has(name) ||\n            htmlIntegrationElements.has(name)) {\n            this.foreignContext.pop();\n        }\n        if (!this.isVoidElement(name)) {\n            const pos = this.stack.lastIndexOf(name);\n            if (pos !== -1) {\n                if (this.cbs.onclosetag) {\n                    let count = this.stack.length - pos;\n                    while (count--) {\n                        // We know the stack has sufficient elements.\n                        this.cbs.onclosetag(this.stack.pop(), count !== 0);\n                    }\n                }\n                else\n                    this.stack.length = pos;\n            }\n            else if (!this.options.xmlMode && name === \"p\") {\n                // Implicit open before close\n                this.emitOpenTag(\"p\");\n                this.closeCurrentTag(true);\n            }\n        }\n        else if (!this.options.xmlMode && name === \"br\") {\n            // We can't use `emitOpenTag` for implicit open, as `br` would be implicitly closed.\n            (_b = (_a = this.cbs).onopentagname) === null || _b === void 0 ? void 0 : _b.call(_a, \"br\");\n            (_d = (_c = this.cbs).onopentag) === null || _d === void 0 ? void 0 : _d.call(_c, \"br\", {}, true);\n            (_f = (_e = this.cbs).onclosetag) === null || _f === void 0 ? void 0 : _f.call(_e, \"br\", false);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onselfclosingtag(endIndex) {\n        this.endIndex = endIndex;\n        if (this.options.xmlMode ||\n            this.options.recognizeSelfClosing ||\n            this.foreignContext[this.foreignContext.length - 1]) {\n            this.closeCurrentTag(false);\n            // Set `startIndex` for next node\n            this.startIndex = endIndex + 1;\n        }\n        else {\n            // Ignore the fact that the tag is self-closing.\n            this.onopentagend(endIndex);\n        }\n    }\n    closeCurrentTag(isOpenImplied) {\n        var _a, _b;\n        const name = this.tagname;\n        this.endOpenTag(isOpenImplied);\n        // Self-closing tags will be on the top of the stack\n        if (this.stack[this.stack.length - 1] === name) {\n            // If the opening tag isn't implied, the closing tag has to be implied.\n            (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, name, !isOpenImplied);\n            this.stack.pop();\n        }\n    }\n    /** @internal */\n    onattribname(start, endIndex) {\n        this.startIndex = start;\n        const name = this.getSlice(start, endIndex);\n        this.attribname = this.lowerCaseAttributeNames\n            ? name.toLowerCase()\n            : name;\n    }\n    /** @internal */\n    onattribdata(start, endIndex) {\n        this.attribvalue += this.getSlice(start, endIndex);\n    }\n    /** @internal */\n    onattribentity(cp) {\n        this.attribvalue += (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(cp);\n    }\n    /** @internal */\n    onattribend(quote, endIndex) {\n        var _a, _b;\n        this.endIndex = endIndex;\n        (_b = (_a = this.cbs).onattribute) === null || _b === void 0 ? void 0 : _b.call(_a, this.attribname, this.attribvalue, quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.Double\n            ? '\"'\n            : quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.Single\n                ? \"'\"\n                : quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.NoValue\n                    ? undefined\n                    : null);\n        if (this.attribs &&\n            !Object.prototype.hasOwnProperty.call(this.attribs, this.attribname)) {\n            this.attribs[this.attribname] = this.attribvalue;\n        }\n        this.attribvalue = \"\";\n    }\n    getInstructionName(value) {\n        const index = value.search(reNameEnd);\n        let name = index < 0 ? value : value.substr(0, index);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        return name;\n    }\n    /** @internal */\n    ondeclaration(start, endIndex) {\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex);\n        if (this.cbs.onprocessinginstruction) {\n            const name = this.getInstructionName(value);\n            this.cbs.onprocessinginstruction(`!${name}`, `!${value}`);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onprocessinginstruction(start, endIndex) {\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex);\n        if (this.cbs.onprocessinginstruction) {\n            const name = this.getInstructionName(value);\n            this.cbs.onprocessinginstruction(`?${name}`, `?${value}`);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    oncomment(start, endIndex, offset) {\n        var _a, _b, _c, _d;\n        this.endIndex = endIndex;\n        (_b = (_a = this.cbs).oncomment) === null || _b === void 0 ? void 0 : _b.call(_a, this.getSlice(start, endIndex - offset));\n        (_d = (_c = this.cbs).oncommentend) === null || _d === void 0 ? void 0 : _d.call(_c);\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    oncdata(start, endIndex, offset) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex - offset);\n        if (this.options.xmlMode || this.options.recognizeCDATA) {\n            (_b = (_a = this.cbs).oncdatastart) === null || _b === void 0 ? void 0 : _b.call(_a);\n            (_d = (_c = this.cbs).ontext) === null || _d === void 0 ? void 0 : _d.call(_c, value);\n            (_f = (_e = this.cbs).oncdataend) === null || _f === void 0 ? void 0 : _f.call(_e);\n        }\n        else {\n            (_h = (_g = this.cbs).oncomment) === null || _h === void 0 ? void 0 : _h.call(_g, `[CDATA[${value}]]`);\n            (_k = (_j = this.cbs).oncommentend) === null || _k === void 0 ? void 0 : _k.call(_j);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onend() {\n        var _a, _b;\n        if (this.cbs.onclosetag) {\n            // Set the end index for all remaining tags\n            this.endIndex = this.startIndex;\n            for (let index = this.stack.length; index > 0; this.cbs.onclosetag(this.stack[--index], true))\n                ;\n        }\n        (_b = (_a = this.cbs).onend) === null || _b === void 0 ? void 0 : _b.call(_a);\n    }\n    /**\n     * Resets the parser to a blank state, ready to parse a new HTML document\n     */\n    reset() {\n        var _a, _b, _c, _d;\n        (_b = (_a = this.cbs).onreset) === null || _b === void 0 ? void 0 : _b.call(_a);\n        this.tokenizer.reset();\n        this.tagname = \"\";\n        this.attribname = \"\";\n        this.attribs = null;\n        this.stack.length = 0;\n        this.startIndex = 0;\n        this.endIndex = 0;\n        (_d = (_c = this.cbs).onparserinit) === null || _d === void 0 ? void 0 : _d.call(_c, this);\n        this.buffers.length = 0;\n        this.bufferOffset = 0;\n        this.writeIndex = 0;\n        this.ended = false;\n    }\n    /**\n     * Resets the parser, then parses a complete document and\n     * pushes it to the handler.\n     *\n     * @param data Document to parse.\n     */\n    parseComplete(data) {\n        this.reset();\n        this.end(data);\n    }\n    getSlice(start, end) {\n        while (start - this.bufferOffset >= this.buffers[0].length) {\n            this.shiftBuffer();\n        }\n        let slice = this.buffers[0].slice(start - this.bufferOffset, end - this.bufferOffset);\n        while (end - this.bufferOffset > this.buffers[0].length) {\n            this.shiftBuffer();\n            slice += this.buffers[0].slice(0, end - this.bufferOffset);\n        }\n        return slice;\n    }\n    shiftBuffer() {\n        this.bufferOffset += this.buffers[0].length;\n        this.writeIndex--;\n        this.buffers.shift();\n    }\n    /**\n     * Parses a chunk of data and calls the corresponding callbacks.\n     *\n     * @param chunk Chunk to parse.\n     */\n    write(chunk) {\n        var _a, _b;\n        if (this.ended) {\n            (_b = (_a = this.cbs).onerror) === null || _b === void 0 ? void 0 : _b.call(_a, new Error(\".write() after done!\"));\n            return;\n        }\n        this.buffers.push(chunk);\n        if (this.tokenizer.running) {\n            this.tokenizer.write(chunk);\n            this.writeIndex++;\n        }\n    }\n    /**\n     * Parses the end of the buffer and clears the stack, calls onend.\n     *\n     * @param chunk Optional final chunk to parse.\n     */\n    end(chunk) {\n        var _a, _b;\n        if (this.ended) {\n            (_b = (_a = this.cbs).onerror) === null || _b === void 0 ? void 0 : _b.call(_a, new Error(\".end() after done!\"));\n            return;\n        }\n        if (chunk)\n            this.write(chunk);\n        this.ended = true;\n        this.tokenizer.end();\n    }\n    /**\n     * Pauses parsing. The parser won't emit events until `resume` is called.\n     */\n    pause() {\n        this.tokenizer.pause();\n    }\n    /**\n     * Resumes parsing after `pause` was called.\n     */\n    resume() {\n        this.tokenizer.resume();\n        while (this.tokenizer.running &&\n            this.writeIndex < this.buffers.length) {\n            this.tokenizer.write(this.buffers[this.writeIndex++]);\n        }\n        if (this.ended)\n            this.tokenizer.end();\n    }\n    /**\n     * Alias of `write`, for backwards compatibility.\n     *\n     * @param chunk Chunk to parse.\n     * @deprecated\n     */\n    parseChunk(chunk) {\n        this.write(chunk);\n    }\n    /**\n     * Alias of `end`, for backwards compatibility.\n     *\n     * @param chunk Optional final chunk to parse.\n     * @deprecated\n     */\n    done(chunk) {\n        this.end(chunk);\n    }\n}\n//# sourceMappingURL=Parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/htmlparser2/lib/esm/Parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/htmlparser2/lib/esm/Tokenizer.js":
/*!*******************************************************!*\
  !*** ./node_modules/htmlparser2/lib/esm/Tokenizer.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuoteType: () => (/* binding */ QuoteType),\n/* harmony export */   \"default\": () => (/* binding */ Tokenizer)\n/* harmony export */ });\n/* harmony import */ var entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! entities/lib/decode.js */ \"(rsc)/./node_modules/entities/lib/esm/decode.js\");\n\nvar CharCodes;\n(function (CharCodes) {\n    CharCodes[CharCodes[\"Tab\"] = 9] = \"Tab\";\n    CharCodes[CharCodes[\"NewLine\"] = 10] = \"NewLine\";\n    CharCodes[CharCodes[\"FormFeed\"] = 12] = \"FormFeed\";\n    CharCodes[CharCodes[\"CarriageReturn\"] = 13] = \"CarriageReturn\";\n    CharCodes[CharCodes[\"Space\"] = 32] = \"Space\";\n    CharCodes[CharCodes[\"ExclamationMark\"] = 33] = \"ExclamationMark\";\n    CharCodes[CharCodes[\"Number\"] = 35] = \"Number\";\n    CharCodes[CharCodes[\"Amp\"] = 38] = \"Amp\";\n    CharCodes[CharCodes[\"SingleQuote\"] = 39] = \"SingleQuote\";\n    CharCodes[CharCodes[\"DoubleQuote\"] = 34] = \"DoubleQuote\";\n    CharCodes[CharCodes[\"Dash\"] = 45] = \"Dash\";\n    CharCodes[CharCodes[\"Slash\"] = 47] = \"Slash\";\n    CharCodes[CharCodes[\"Zero\"] = 48] = \"Zero\";\n    CharCodes[CharCodes[\"Nine\"] = 57] = \"Nine\";\n    CharCodes[CharCodes[\"Semi\"] = 59] = \"Semi\";\n    CharCodes[CharCodes[\"Lt\"] = 60] = \"Lt\";\n    CharCodes[CharCodes[\"Eq\"] = 61] = \"Eq\";\n    CharCodes[CharCodes[\"Gt\"] = 62] = \"Gt\";\n    CharCodes[CharCodes[\"Questionmark\"] = 63] = \"Questionmark\";\n    CharCodes[CharCodes[\"UpperA\"] = 65] = \"UpperA\";\n    CharCodes[CharCodes[\"LowerA\"] = 97] = \"LowerA\";\n    CharCodes[CharCodes[\"UpperF\"] = 70] = \"UpperF\";\n    CharCodes[CharCodes[\"LowerF\"] = 102] = \"LowerF\";\n    CharCodes[CharCodes[\"UpperZ\"] = 90] = \"UpperZ\";\n    CharCodes[CharCodes[\"LowerZ\"] = 122] = \"LowerZ\";\n    CharCodes[CharCodes[\"LowerX\"] = 120] = \"LowerX\";\n    CharCodes[CharCodes[\"OpeningSquareBracket\"] = 91] = \"OpeningSquareBracket\";\n})(CharCodes || (CharCodes = {}));\n/** All the states the tokenizer can be in. */\nvar State;\n(function (State) {\n    State[State[\"Text\"] = 1] = \"Text\";\n    State[State[\"BeforeTagName\"] = 2] = \"BeforeTagName\";\n    State[State[\"InTagName\"] = 3] = \"InTagName\";\n    State[State[\"InSelfClosingTag\"] = 4] = \"InSelfClosingTag\";\n    State[State[\"BeforeClosingTagName\"] = 5] = \"BeforeClosingTagName\";\n    State[State[\"InClosingTagName\"] = 6] = \"InClosingTagName\";\n    State[State[\"AfterClosingTagName\"] = 7] = \"AfterClosingTagName\";\n    // Attributes\n    State[State[\"BeforeAttributeName\"] = 8] = \"BeforeAttributeName\";\n    State[State[\"InAttributeName\"] = 9] = \"InAttributeName\";\n    State[State[\"AfterAttributeName\"] = 10] = \"AfterAttributeName\";\n    State[State[\"BeforeAttributeValue\"] = 11] = \"BeforeAttributeValue\";\n    State[State[\"InAttributeValueDq\"] = 12] = \"InAttributeValueDq\";\n    State[State[\"InAttributeValueSq\"] = 13] = \"InAttributeValueSq\";\n    State[State[\"InAttributeValueNq\"] = 14] = \"InAttributeValueNq\";\n    // Declarations\n    State[State[\"BeforeDeclaration\"] = 15] = \"BeforeDeclaration\";\n    State[State[\"InDeclaration\"] = 16] = \"InDeclaration\";\n    // Processing instructions\n    State[State[\"InProcessingInstruction\"] = 17] = \"InProcessingInstruction\";\n    // Comments & CDATA\n    State[State[\"BeforeComment\"] = 18] = \"BeforeComment\";\n    State[State[\"CDATASequence\"] = 19] = \"CDATASequence\";\n    State[State[\"InSpecialComment\"] = 20] = \"InSpecialComment\";\n    State[State[\"InCommentLike\"] = 21] = \"InCommentLike\";\n    // Special tags\n    State[State[\"BeforeSpecialS\"] = 22] = \"BeforeSpecialS\";\n    State[State[\"SpecialStartSequence\"] = 23] = \"SpecialStartSequence\";\n    State[State[\"InSpecialTag\"] = 24] = \"InSpecialTag\";\n    State[State[\"BeforeEntity\"] = 25] = \"BeforeEntity\";\n    State[State[\"BeforeNumericEntity\"] = 26] = \"BeforeNumericEntity\";\n    State[State[\"InNamedEntity\"] = 27] = \"InNamedEntity\";\n    State[State[\"InNumericEntity\"] = 28] = \"InNumericEntity\";\n    State[State[\"InHexEntity\"] = 29] = \"InHexEntity\";\n})(State || (State = {}));\nfunction isWhitespace(c) {\n    return (c === CharCodes.Space ||\n        c === CharCodes.NewLine ||\n        c === CharCodes.Tab ||\n        c === CharCodes.FormFeed ||\n        c === CharCodes.CarriageReturn);\n}\nfunction isEndOfTagSection(c) {\n    return c === CharCodes.Slash || c === CharCodes.Gt || isWhitespace(c);\n}\nfunction isNumber(c) {\n    return c >= CharCodes.Zero && c <= CharCodes.Nine;\n}\nfunction isASCIIAlpha(c) {\n    return ((c >= CharCodes.LowerA && c <= CharCodes.LowerZ) ||\n        (c >= CharCodes.UpperA && c <= CharCodes.UpperZ));\n}\nfunction isHexDigit(c) {\n    return ((c >= CharCodes.UpperA && c <= CharCodes.UpperF) ||\n        (c >= CharCodes.LowerA && c <= CharCodes.LowerF));\n}\nvar QuoteType;\n(function (QuoteType) {\n    QuoteType[QuoteType[\"NoValue\"] = 0] = \"NoValue\";\n    QuoteType[QuoteType[\"Unquoted\"] = 1] = \"Unquoted\";\n    QuoteType[QuoteType[\"Single\"] = 2] = \"Single\";\n    QuoteType[QuoteType[\"Double\"] = 3] = \"Double\";\n})(QuoteType || (QuoteType = {}));\n/**\n * Sequences used to match longer strings.\n *\n * We don't have `Script`, `Style`, or `Title` here. Instead, we re-use the *End\n * sequences with an increased offset.\n */\nconst Sequences = {\n    Cdata: new Uint8Array([0x43, 0x44, 0x41, 0x54, 0x41, 0x5b]),\n    CdataEnd: new Uint8Array([0x5d, 0x5d, 0x3e]),\n    CommentEnd: new Uint8Array([0x2d, 0x2d, 0x3e]),\n    ScriptEnd: new Uint8Array([0x3c, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74]),\n    StyleEnd: new Uint8Array([0x3c, 0x2f, 0x73, 0x74, 0x79, 0x6c, 0x65]),\n    TitleEnd: new Uint8Array([0x3c, 0x2f, 0x74, 0x69, 0x74, 0x6c, 0x65]), // `</title`\n};\nclass Tokenizer {\n    constructor({ xmlMode = false, decodeEntities = true, }, cbs) {\n        this.cbs = cbs;\n        /** The current state the tokenizer is in. */\n        this.state = State.Text;\n        /** The read buffer. */\n        this.buffer = \"\";\n        /** The beginning of the section that is currently being read. */\n        this.sectionStart = 0;\n        /** The index within the buffer that we are currently looking at. */\n        this.index = 0;\n        /** Some behavior, eg. when decoding entities, is done while we are in another state. This keeps track of the other state type. */\n        this.baseState = State.Text;\n        /** For special parsing behavior inside of script and style tags. */\n        this.isSpecial = false;\n        /** Indicates whether the tokenizer has been paused. */\n        this.running = true;\n        /** The offset of the current buffer. */\n        this.offset = 0;\n        this.currentSequence = undefined;\n        this.sequenceIndex = 0;\n        this.trieIndex = 0;\n        this.trieCurrent = 0;\n        /** For named entities, the index of the value. For numeric entities, the code point. */\n        this.entityResult = 0;\n        this.entityExcess = 0;\n        this.xmlMode = xmlMode;\n        this.decodeEntities = decodeEntities;\n        this.entityTrie = xmlMode ? entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.xmlDecodeTree : entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.htmlDecodeTree;\n    }\n    reset() {\n        this.state = State.Text;\n        this.buffer = \"\";\n        this.sectionStart = 0;\n        this.index = 0;\n        this.baseState = State.Text;\n        this.currentSequence = undefined;\n        this.running = true;\n        this.offset = 0;\n    }\n    write(chunk) {\n        this.offset += this.buffer.length;\n        this.buffer = chunk;\n        this.parse();\n    }\n    end() {\n        if (this.running)\n            this.finish();\n    }\n    pause() {\n        this.running = false;\n    }\n    resume() {\n        this.running = true;\n        if (this.index < this.buffer.length + this.offset) {\n            this.parse();\n        }\n    }\n    /**\n     * The current index within all of the written data.\n     */\n    getIndex() {\n        return this.index;\n    }\n    /**\n     * The start of the current section.\n     */\n    getSectionStart() {\n        return this.sectionStart;\n    }\n    stateText(c) {\n        if (c === CharCodes.Lt ||\n            (!this.decodeEntities && this.fastForwardTo(CharCodes.Lt))) {\n            if (this.index > this.sectionStart) {\n                this.cbs.ontext(this.sectionStart, this.index);\n            }\n            this.state = State.BeforeTagName;\n            this.sectionStart = this.index;\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.state = State.BeforeEntity;\n        }\n    }\n    stateSpecialStartSequence(c) {\n        const isEnd = this.sequenceIndex === this.currentSequence.length;\n        const isMatch = isEnd\n            ? // If we are at the end of the sequence, make sure the tag name has ended\n                isEndOfTagSection(c)\n            : // Otherwise, do a case-insensitive comparison\n                (c | 0x20) === this.currentSequence[this.sequenceIndex];\n        if (!isMatch) {\n            this.isSpecial = false;\n        }\n        else if (!isEnd) {\n            this.sequenceIndex++;\n            return;\n        }\n        this.sequenceIndex = 0;\n        this.state = State.InTagName;\n        this.stateInTagName(c);\n    }\n    /** Look for an end tag. For <title> tags, also decode entities. */\n    stateInSpecialTag(c) {\n        if (this.sequenceIndex === this.currentSequence.length) {\n            if (c === CharCodes.Gt || isWhitespace(c)) {\n                const endOfText = this.index - this.currentSequence.length;\n                if (this.sectionStart < endOfText) {\n                    // Spoof the index so that reported locations match up.\n                    const actualIndex = this.index;\n                    this.index = endOfText;\n                    this.cbs.ontext(this.sectionStart, endOfText);\n                    this.index = actualIndex;\n                }\n                this.isSpecial = false;\n                this.sectionStart = endOfText + 2; // Skip over the `</`\n                this.stateInClosingTagName(c);\n                return; // We are done; skip the rest of the function.\n            }\n            this.sequenceIndex = 0;\n        }\n        if ((c | 0x20) === this.currentSequence[this.sequenceIndex]) {\n            this.sequenceIndex += 1;\n        }\n        else if (this.sequenceIndex === 0) {\n            if (this.currentSequence === Sequences.TitleEnd) {\n                // We have to parse entities in <title> tags.\n                if (this.decodeEntities && c === CharCodes.Amp) {\n                    this.state = State.BeforeEntity;\n                }\n            }\n            else if (this.fastForwardTo(CharCodes.Lt)) {\n                // Outside of <title> tags, we can fast-forward.\n                this.sequenceIndex = 1;\n            }\n        }\n        else {\n            // If we see a `<`, set the sequence index to 1; useful for eg. `<</script>`.\n            this.sequenceIndex = Number(c === CharCodes.Lt);\n        }\n    }\n    stateCDATASequence(c) {\n        if (c === Sequences.Cdata[this.sequenceIndex]) {\n            if (++this.sequenceIndex === Sequences.Cdata.length) {\n                this.state = State.InCommentLike;\n                this.currentSequence = Sequences.CdataEnd;\n                this.sequenceIndex = 0;\n                this.sectionStart = this.index + 1;\n            }\n        }\n        else {\n            this.sequenceIndex = 0;\n            this.state = State.InDeclaration;\n            this.stateInDeclaration(c); // Reconsume the character\n        }\n    }\n    /**\n     * When we wait for one specific character, we can speed things up\n     * by skipping through the buffer until we find it.\n     *\n     * @returns Whether the character was found.\n     */\n    fastForwardTo(c) {\n        while (++this.index < this.buffer.length + this.offset) {\n            if (this.buffer.charCodeAt(this.index - this.offset) === c) {\n                return true;\n            }\n        }\n        /*\n         * We increment the index at the end of the `parse` loop,\n         * so set it to `buffer.length - 1` here.\n         *\n         * TODO: Refactor `parse` to increment index before calling states.\n         */\n        this.index = this.buffer.length + this.offset - 1;\n        return false;\n    }\n    /**\n     * Comments and CDATA end with `-->` and `]]>`.\n     *\n     * Their common qualities are:\n     * - Their end sequences have a distinct character they start with.\n     * - That character is then repeated, so we have to check multiple repeats.\n     * - All characters but the start character of the sequence can be skipped.\n     */\n    stateInCommentLike(c) {\n        if (c === this.currentSequence[this.sequenceIndex]) {\n            if (++this.sequenceIndex === this.currentSequence.length) {\n                if (this.currentSequence === Sequences.CdataEnd) {\n                    this.cbs.oncdata(this.sectionStart, this.index, 2);\n                }\n                else {\n                    this.cbs.oncomment(this.sectionStart, this.index, 2);\n                }\n                this.sequenceIndex = 0;\n                this.sectionStart = this.index + 1;\n                this.state = State.Text;\n            }\n        }\n        else if (this.sequenceIndex === 0) {\n            // Fast-forward to the first character of the sequence\n            if (this.fastForwardTo(this.currentSequence[0])) {\n                this.sequenceIndex = 1;\n            }\n        }\n        else if (c !== this.currentSequence[this.sequenceIndex - 1]) {\n            // Allow long sequences, eg. --->, ]]]>\n            this.sequenceIndex = 0;\n        }\n    }\n    /**\n     * HTML only allows ASCII alpha characters (a-z and A-Z) at the beginning of a tag name.\n     *\n     * XML allows a lot more characters here (@see https://www.w3.org/TR/REC-xml/#NT-NameStartChar).\n     * We allow anything that wouldn't end the tag.\n     */\n    isTagStartChar(c) {\n        return this.xmlMode ? !isEndOfTagSection(c) : isASCIIAlpha(c);\n    }\n    startSpecial(sequence, offset) {\n        this.isSpecial = true;\n        this.currentSequence = sequence;\n        this.sequenceIndex = offset;\n        this.state = State.SpecialStartSequence;\n    }\n    stateBeforeTagName(c) {\n        if (c === CharCodes.ExclamationMark) {\n            this.state = State.BeforeDeclaration;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.Questionmark) {\n            this.state = State.InProcessingInstruction;\n            this.sectionStart = this.index + 1;\n        }\n        else if (this.isTagStartChar(c)) {\n            const lower = c | 0x20;\n            this.sectionStart = this.index;\n            if (!this.xmlMode && lower === Sequences.TitleEnd[2]) {\n                this.startSpecial(Sequences.TitleEnd, 3);\n            }\n            else {\n                this.state =\n                    !this.xmlMode && lower === Sequences.ScriptEnd[2]\n                        ? State.BeforeSpecialS\n                        : State.InTagName;\n            }\n        }\n        else if (c === CharCodes.Slash) {\n            this.state = State.BeforeClosingTagName;\n        }\n        else {\n            this.state = State.Text;\n            this.stateText(c);\n        }\n    }\n    stateInTagName(c) {\n        if (isEndOfTagSection(c)) {\n            this.cbs.onopentagname(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n    }\n    stateBeforeClosingTagName(c) {\n        if (isWhitespace(c)) {\n            // Ignore\n        }\n        else if (c === CharCodes.Gt) {\n            this.state = State.Text;\n        }\n        else {\n            this.state = this.isTagStartChar(c)\n                ? State.InClosingTagName\n                : State.InSpecialComment;\n            this.sectionStart = this.index;\n        }\n    }\n    stateInClosingTagName(c) {\n        if (c === CharCodes.Gt || isWhitespace(c)) {\n            this.cbs.onclosetag(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.AfterClosingTagName;\n            this.stateAfterClosingTagName(c);\n        }\n    }\n    stateAfterClosingTagName(c) {\n        // Skip everything until \">\"\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.state = State.Text;\n            this.baseState = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeAttributeName(c) {\n        if (c === CharCodes.Gt) {\n            this.cbs.onopentagend(this.index);\n            if (this.isSpecial) {\n                this.state = State.InSpecialTag;\n                this.sequenceIndex = 0;\n            }\n            else {\n                this.state = State.Text;\n            }\n            this.baseState = this.state;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.Slash) {\n            this.state = State.InSelfClosingTag;\n        }\n        else if (!isWhitespace(c)) {\n            this.state = State.InAttributeName;\n            this.sectionStart = this.index;\n        }\n    }\n    stateInSelfClosingTag(c) {\n        if (c === CharCodes.Gt) {\n            this.cbs.onselfclosingtag(this.index);\n            this.state = State.Text;\n            this.baseState = State.Text;\n            this.sectionStart = this.index + 1;\n            this.isSpecial = false; // Reset special state, in case of self-closing special tags\n        }\n        else if (!isWhitespace(c)) {\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n    }\n    stateInAttributeName(c) {\n        if (c === CharCodes.Eq || isEndOfTagSection(c)) {\n            this.cbs.onattribname(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.AfterAttributeName;\n            this.stateAfterAttributeName(c);\n        }\n    }\n    stateAfterAttributeName(c) {\n        if (c === CharCodes.Eq) {\n            this.state = State.BeforeAttributeValue;\n        }\n        else if (c === CharCodes.Slash || c === CharCodes.Gt) {\n            this.cbs.onattribend(QuoteType.NoValue, this.index);\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n        else if (!isWhitespace(c)) {\n            this.cbs.onattribend(QuoteType.NoValue, this.index);\n            this.state = State.InAttributeName;\n            this.sectionStart = this.index;\n        }\n    }\n    stateBeforeAttributeValue(c) {\n        if (c === CharCodes.DoubleQuote) {\n            this.state = State.InAttributeValueDq;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.SingleQuote) {\n            this.state = State.InAttributeValueSq;\n            this.sectionStart = this.index + 1;\n        }\n        else if (!isWhitespace(c)) {\n            this.sectionStart = this.index;\n            this.state = State.InAttributeValueNq;\n            this.stateInAttributeValueNoQuotes(c); // Reconsume token\n        }\n    }\n    handleInAttributeValue(c, quote) {\n        if (c === quote ||\n            (!this.decodeEntities && this.fastForwardTo(quote))) {\n            this.cbs.onattribdata(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.cbs.onattribend(quote === CharCodes.DoubleQuote\n                ? QuoteType.Double\n                : QuoteType.Single, this.index);\n            this.state = State.BeforeAttributeName;\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.baseState = this.state;\n            this.state = State.BeforeEntity;\n        }\n    }\n    stateInAttributeValueDoubleQuotes(c) {\n        this.handleInAttributeValue(c, CharCodes.DoubleQuote);\n    }\n    stateInAttributeValueSingleQuotes(c) {\n        this.handleInAttributeValue(c, CharCodes.SingleQuote);\n    }\n    stateInAttributeValueNoQuotes(c) {\n        if (isWhitespace(c) || c === CharCodes.Gt) {\n            this.cbs.onattribdata(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.cbs.onattribend(QuoteType.Unquoted, this.index);\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.baseState = this.state;\n            this.state = State.BeforeEntity;\n        }\n    }\n    stateBeforeDeclaration(c) {\n        if (c === CharCodes.OpeningSquareBracket) {\n            this.state = State.CDATASequence;\n            this.sequenceIndex = 0;\n        }\n        else {\n            this.state =\n                c === CharCodes.Dash\n                    ? State.BeforeComment\n                    : State.InDeclaration;\n        }\n    }\n    stateInDeclaration(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.ondeclaration(this.sectionStart, this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateInProcessingInstruction(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.onprocessinginstruction(this.sectionStart, this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeComment(c) {\n        if (c === CharCodes.Dash) {\n            this.state = State.InCommentLike;\n            this.currentSequence = Sequences.CommentEnd;\n            // Allow short comments (eg. <!-->)\n            this.sequenceIndex = 2;\n            this.sectionStart = this.index + 1;\n        }\n        else {\n            this.state = State.InDeclaration;\n        }\n    }\n    stateInSpecialComment(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.oncomment(this.sectionStart, this.index, 0);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeSpecialS(c) {\n        const lower = c | 0x20;\n        if (lower === Sequences.ScriptEnd[3]) {\n            this.startSpecial(Sequences.ScriptEnd, 4);\n        }\n        else if (lower === Sequences.StyleEnd[3]) {\n            this.startSpecial(Sequences.StyleEnd, 4);\n        }\n        else {\n            this.state = State.InTagName;\n            this.stateInTagName(c); // Consume the token again\n        }\n    }\n    stateBeforeEntity(c) {\n        // Start excess with 1 to include the '&'\n        this.entityExcess = 1;\n        this.entityResult = 0;\n        if (c === CharCodes.Number) {\n            this.state = State.BeforeNumericEntity;\n        }\n        else if (c === CharCodes.Amp) {\n            // We have two `&` characters in a row. Stay in the current state.\n        }\n        else {\n            this.trieIndex = 0;\n            this.trieCurrent = this.entityTrie[0];\n            this.state = State.InNamedEntity;\n            this.stateInNamedEntity(c);\n        }\n    }\n    stateInNamedEntity(c) {\n        this.entityExcess += 1;\n        this.trieIndex = (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.determineBranch)(this.entityTrie, this.trieCurrent, this.trieIndex + 1, c);\n        if (this.trieIndex < 0) {\n            this.emitNamedEntity();\n            this.index--;\n            return;\n        }\n        this.trieCurrent = this.entityTrie[this.trieIndex];\n        const masked = this.trieCurrent & entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.BinTrieFlags.VALUE_LENGTH;\n        // If the branch is a value, store it and continue\n        if (masked) {\n            // The mask is the number of bytes of the value, including the current byte.\n            const valueLength = (masked >> 14) - 1;\n            // If we have a legacy entity while parsing strictly, just skip the number of bytes\n            if (!this.allowLegacyEntity() && c !== CharCodes.Semi) {\n                this.trieIndex += valueLength;\n            }\n            else {\n                // Add 1 as we have already incremented the excess\n                const entityStart = this.index - this.entityExcess + 1;\n                if (entityStart > this.sectionStart) {\n                    this.emitPartial(this.sectionStart, entityStart);\n                }\n                // If this is a surrogate pair, consume the next two bytes\n                this.entityResult = this.trieIndex;\n                this.trieIndex += valueLength;\n                this.entityExcess = 0;\n                this.sectionStart = this.index + 1;\n                if (valueLength === 0) {\n                    this.emitNamedEntity();\n                }\n            }\n        }\n    }\n    emitNamedEntity() {\n        this.state = this.baseState;\n        if (this.entityResult === 0) {\n            return;\n        }\n        const valueLength = (this.entityTrie[this.entityResult] & entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.BinTrieFlags.VALUE_LENGTH) >>\n            14;\n        switch (valueLength) {\n            case 1: {\n                this.emitCodePoint(this.entityTrie[this.entityResult] &\n                    ~entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.BinTrieFlags.VALUE_LENGTH);\n                break;\n            }\n            case 2: {\n                this.emitCodePoint(this.entityTrie[this.entityResult + 1]);\n                break;\n            }\n            case 3: {\n                this.emitCodePoint(this.entityTrie[this.entityResult + 1]);\n                this.emitCodePoint(this.entityTrie[this.entityResult + 2]);\n            }\n        }\n    }\n    stateBeforeNumericEntity(c) {\n        if ((c | 0x20) === CharCodes.LowerX) {\n            this.entityExcess++;\n            this.state = State.InHexEntity;\n        }\n        else {\n            this.state = State.InNumericEntity;\n            this.stateInNumericEntity(c);\n        }\n    }\n    emitNumericEntity(strict) {\n        const entityStart = this.index - this.entityExcess - 1;\n        const numberStart = entityStart + 2 + Number(this.state === State.InHexEntity);\n        if (numberStart !== this.index) {\n            // Emit leading data if any\n            if (entityStart > this.sectionStart) {\n                this.emitPartial(this.sectionStart, entityStart);\n            }\n            this.sectionStart = this.index + Number(strict);\n            this.emitCodePoint((0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.replaceCodePoint)(this.entityResult));\n        }\n        this.state = this.baseState;\n    }\n    stateInNumericEntity(c) {\n        if (c === CharCodes.Semi) {\n            this.emitNumericEntity(true);\n        }\n        else if (isNumber(c)) {\n            this.entityResult = this.entityResult * 10 + (c - CharCodes.Zero);\n            this.entityExcess++;\n        }\n        else {\n            if (this.allowLegacyEntity()) {\n                this.emitNumericEntity(false);\n            }\n            else {\n                this.state = this.baseState;\n            }\n            this.index--;\n        }\n    }\n    stateInHexEntity(c) {\n        if (c === CharCodes.Semi) {\n            this.emitNumericEntity(true);\n        }\n        else if (isNumber(c)) {\n            this.entityResult = this.entityResult * 16 + (c - CharCodes.Zero);\n            this.entityExcess++;\n        }\n        else if (isHexDigit(c)) {\n            this.entityResult =\n                this.entityResult * 16 + ((c | 0x20) - CharCodes.LowerA + 10);\n            this.entityExcess++;\n        }\n        else {\n            if (this.allowLegacyEntity()) {\n                this.emitNumericEntity(false);\n            }\n            else {\n                this.state = this.baseState;\n            }\n            this.index--;\n        }\n    }\n    allowLegacyEntity() {\n        return (!this.xmlMode &&\n            (this.baseState === State.Text ||\n                this.baseState === State.InSpecialTag));\n    }\n    /**\n     * Remove data that has already been consumed from the buffer.\n     */\n    cleanup() {\n        // If we are inside of text or attributes, emit what we already have.\n        if (this.running && this.sectionStart !== this.index) {\n            if (this.state === State.Text ||\n                (this.state === State.InSpecialTag && this.sequenceIndex === 0)) {\n                this.cbs.ontext(this.sectionStart, this.index);\n                this.sectionStart = this.index;\n            }\n            else if (this.state === State.InAttributeValueDq ||\n                this.state === State.InAttributeValueSq ||\n                this.state === State.InAttributeValueNq) {\n                this.cbs.onattribdata(this.sectionStart, this.index);\n                this.sectionStart = this.index;\n            }\n        }\n    }\n    shouldContinue() {\n        return this.index < this.buffer.length + this.offset && this.running;\n    }\n    /**\n     * Iterates through the buffer, calling the function corresponding to the current state.\n     *\n     * States that are more likely to be hit are higher up, as a performance improvement.\n     */\n    parse() {\n        while (this.shouldContinue()) {\n            const c = this.buffer.charCodeAt(this.index - this.offset);\n            switch (this.state) {\n                case State.Text: {\n                    this.stateText(c);\n                    break;\n                }\n                case State.SpecialStartSequence: {\n                    this.stateSpecialStartSequence(c);\n                    break;\n                }\n                case State.InSpecialTag: {\n                    this.stateInSpecialTag(c);\n                    break;\n                }\n                case State.CDATASequence: {\n                    this.stateCDATASequence(c);\n                    break;\n                }\n                case State.InAttributeValueDq: {\n                    this.stateInAttributeValueDoubleQuotes(c);\n                    break;\n                }\n                case State.InAttributeName: {\n                    this.stateInAttributeName(c);\n                    break;\n                }\n                case State.InCommentLike: {\n                    this.stateInCommentLike(c);\n                    break;\n                }\n                case State.InSpecialComment: {\n                    this.stateInSpecialComment(c);\n                    break;\n                }\n                case State.BeforeAttributeName: {\n                    this.stateBeforeAttributeName(c);\n                    break;\n                }\n                case State.InTagName: {\n                    this.stateInTagName(c);\n                    break;\n                }\n                case State.InClosingTagName: {\n                    this.stateInClosingTagName(c);\n                    break;\n                }\n                case State.BeforeTagName: {\n                    this.stateBeforeTagName(c);\n                    break;\n                }\n                case State.AfterAttributeName: {\n                    this.stateAfterAttributeName(c);\n                    break;\n                }\n                case State.InAttributeValueSq: {\n                    this.stateInAttributeValueSingleQuotes(c);\n                    break;\n                }\n                case State.BeforeAttributeValue: {\n                    this.stateBeforeAttributeValue(c);\n                    break;\n                }\n                case State.BeforeClosingTagName: {\n                    this.stateBeforeClosingTagName(c);\n                    break;\n                }\n                case State.AfterClosingTagName: {\n                    this.stateAfterClosingTagName(c);\n                    break;\n                }\n                case State.BeforeSpecialS: {\n                    this.stateBeforeSpecialS(c);\n                    break;\n                }\n                case State.InAttributeValueNq: {\n                    this.stateInAttributeValueNoQuotes(c);\n                    break;\n                }\n                case State.InSelfClosingTag: {\n                    this.stateInSelfClosingTag(c);\n                    break;\n                }\n                case State.InDeclaration: {\n                    this.stateInDeclaration(c);\n                    break;\n                }\n                case State.BeforeDeclaration: {\n                    this.stateBeforeDeclaration(c);\n                    break;\n                }\n                case State.BeforeComment: {\n                    this.stateBeforeComment(c);\n                    break;\n                }\n                case State.InProcessingInstruction: {\n                    this.stateInProcessingInstruction(c);\n                    break;\n                }\n                case State.InNamedEntity: {\n                    this.stateInNamedEntity(c);\n                    break;\n                }\n                case State.BeforeEntity: {\n                    this.stateBeforeEntity(c);\n                    break;\n                }\n                case State.InHexEntity: {\n                    this.stateInHexEntity(c);\n                    break;\n                }\n                case State.InNumericEntity: {\n                    this.stateInNumericEntity(c);\n                    break;\n                }\n                default: {\n                    // `this._state === State.BeforeNumericEntity`\n                    this.stateBeforeNumericEntity(c);\n                }\n            }\n            this.index++;\n        }\n        this.cleanup();\n    }\n    finish() {\n        if (this.state === State.InNamedEntity) {\n            this.emitNamedEntity();\n        }\n        // If there is remaining data, emit it in a reasonable way\n        if (this.sectionStart < this.index) {\n            this.handleTrailingData();\n        }\n        this.cbs.onend();\n    }\n    /** Handle any trailing data. */\n    handleTrailingData() {\n        const endIndex = this.buffer.length + this.offset;\n        if (this.state === State.InCommentLike) {\n            if (this.currentSequence === Sequences.CdataEnd) {\n                this.cbs.oncdata(this.sectionStart, endIndex, 0);\n            }\n            else {\n                this.cbs.oncomment(this.sectionStart, endIndex, 0);\n            }\n        }\n        else if (this.state === State.InNumericEntity &&\n            this.allowLegacyEntity()) {\n            this.emitNumericEntity(false);\n            // All trailing data will have been consumed\n        }\n        else if (this.state === State.InHexEntity &&\n            this.allowLegacyEntity()) {\n            this.emitNumericEntity(false);\n            // All trailing data will have been consumed\n        }\n        else if (this.state === State.InTagName ||\n            this.state === State.BeforeAttributeName ||\n            this.state === State.BeforeAttributeValue ||\n            this.state === State.AfterAttributeName ||\n            this.state === State.InAttributeName ||\n            this.state === State.InAttributeValueSq ||\n            this.state === State.InAttributeValueDq ||\n            this.state === State.InAttributeValueNq ||\n            this.state === State.InClosingTagName) {\n            /*\n             * If we are currently in an opening or closing tag, us not calling the\n             * respective callback signals that the tag should be ignored.\n             */\n        }\n        else {\n            this.cbs.ontext(this.sectionStart, endIndex);\n        }\n    }\n    emitPartial(start, endIndex) {\n        if (this.baseState !== State.Text &&\n            this.baseState !== State.InSpecialTag) {\n            this.cbs.onattribdata(start, endIndex);\n        }\n        else {\n            this.cbs.ontext(start, endIndex);\n        }\n    }\n    emitCodePoint(cp) {\n        if (this.baseState !== State.Text &&\n            this.baseState !== State.InSpecialTag) {\n            this.cbs.onattribentity(cp);\n        }\n        else {\n            this.cbs.ontextentity(cp);\n        }\n    }\n}\n//# sourceMappingURL=Tokenizer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/htmlparser2/lib/esm/Tokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/htmlparser2/lib/esm/index.js":
/*!***************************************************!*\
  !*** ./node_modules/htmlparser2/lib/esm/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultHandler: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler),\n/* harmony export */   DomHandler: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler),\n/* harmony export */   DomUtils: () => (/* reexport module object */ domutils__WEBPACK_IMPORTED_MODULE_4__),\n/* harmony export */   ElementType: () => (/* reexport module object */ domelementtype__WEBPACK_IMPORTED_MODULE_3__),\n/* harmony export */   Parser: () => (/* reexport safe */ _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser),\n/* harmony export */   Tokenizer: () => (/* reexport safe */ _Tokenizer_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   createDomStream: () => (/* binding */ createDomStream),\n/* harmony export */   getFeed: () => (/* reexport safe */ domutils__WEBPACK_IMPORTED_MODULE_4__.getFeed),\n/* harmony export */   parseDOM: () => (/* binding */ parseDOM),\n/* harmony export */   parseDocument: () => (/* binding */ parseDocument),\n/* harmony export */   parseFeed: () => (/* binding */ parseFeed)\n/* harmony export */ });\n/* harmony import */ var _Parser_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Parser.js */ \"(rsc)/./node_modules/htmlparser2/lib/esm/Parser.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var _Tokenizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Tokenizer.js */ \"(rsc)/./node_modules/htmlparser2/lib/esm/Tokenizer.js\");\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n\n\n\n\n// Helper methods\n/**\n * Parses the data, returns the resulting document.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM builder.\n */\nfunction parseDocument(data, options) {\n    const handler = new domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler(undefined, options);\n    new _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser(handler, options).end(data);\n    return handler.root;\n}\n/**\n * Parses data, returns an array of the root nodes.\n *\n * Note that the root nodes still have a `Document` node as their parent.\n * Use `parseDocument` to get the `Document` node instead.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM builder.\n * @deprecated Use `parseDocument` instead.\n */\nfunction parseDOM(data, options) {\n    return parseDocument(data, options).children;\n}\n/**\n * Creates a parser instance, with an attached DOM handler.\n *\n * @param callback A callback that will be called once parsing has been completed.\n * @param options Optional options for the parser and DOM builder.\n * @param elementCallback An optional callback that will be called every time a tag has been completed inside of the DOM.\n */\nfunction createDomStream(callback, options, elementCallback) {\n    const handler = new domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler(callback, options, elementCallback);\n    return new _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser(handler, options);\n}\n\n/*\n * All of the following exports exist for backwards-compatibility.\n * They should probably be removed eventually.\n */\n\n\n\nconst parseFeedDefaultOptions = { xmlMode: true };\n/**\n * Parse a feed.\n *\n * @param feed The feed that should be parsed, as a string.\n * @param options Optionally, options for parsing. When using this, you should set `xmlMode` to `true`.\n */\nfunction parseFeed(feed, options = parseFeedDefaultOptions) {\n    return (0,domutils__WEBPACK_IMPORTED_MODULE_4__.getFeed)(parseDOM(feed, options));\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaHRtbHBhcnNlcjIvbGliL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXFDO0FBQ0E7QUFDSTtBQUdTO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCx3QkFBd0Isa0RBQVU7QUFDbEMsUUFBUSw4Q0FBTTtBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCx3QkFBd0Isa0RBQVU7QUFDbEMsZUFBZSw4Q0FBTTtBQUNyQjtBQUN1RDtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUM4QztBQUNYO0FBQ0E7QUFDbkMsa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsV0FBVyxpREFBTztBQUNsQjtBQUNxQztBQUNyQyIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9ub2RlX21vZHVsZXMvaHRtbHBhcnNlcjIvbGliL2VzbS9pbmRleC5qcz9jNWMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhcnNlciB9IGZyb20gXCIuL1BhcnNlci5qc1wiO1xuZXhwb3J0IHsgUGFyc2VyIH0gZnJvbSBcIi4vUGFyc2VyLmpzXCI7XG5pbXBvcnQgeyBEb21IYW5kbGVyLCB9IGZyb20gXCJkb21oYW5kbGVyXCI7XG5leHBvcnQgeyBEb21IYW5kbGVyLCBcbi8vIE9sZCBuYW1lIGZvciBEb21IYW5kbGVyXG5Eb21IYW5kbGVyIGFzIERlZmF1bHRIYW5kbGVyLCB9IGZyb20gXCJkb21oYW5kbGVyXCI7XG4vLyBIZWxwZXIgbWV0aG9kc1xuLyoqXG4gKiBQYXJzZXMgdGhlIGRhdGEsIHJldHVybnMgdGhlIHJlc3VsdGluZyBkb2N1bWVudC5cbiAqXG4gKiBAcGFyYW0gZGF0YSBUaGUgZGF0YSB0aGF0IHNob3VsZCBiZSBwYXJzZWQuXG4gKiBAcGFyYW0gb3B0aW9ucyBPcHRpb25hbCBvcHRpb25zIGZvciB0aGUgcGFyc2VyIGFuZCBET00gYnVpbGRlci5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhcnNlRG9jdW1lbnQoZGF0YSwgb3B0aW9ucykge1xuICAgIGNvbnN0IGhhbmRsZXIgPSBuZXcgRG9tSGFuZGxlcih1bmRlZmluZWQsIG9wdGlvbnMpO1xuICAgIG5ldyBQYXJzZXIoaGFuZGxlciwgb3B0aW9ucykuZW5kKGRhdGEpO1xuICAgIHJldHVybiBoYW5kbGVyLnJvb3Q7XG59XG4vKipcbiAqIFBhcnNlcyBkYXRhLCByZXR1cm5zIGFuIGFycmF5IG9mIHRoZSByb290IG5vZGVzLlxuICpcbiAqIE5vdGUgdGhhdCB0aGUgcm9vdCBub2RlcyBzdGlsbCBoYXZlIGEgYERvY3VtZW50YCBub2RlIGFzIHRoZWlyIHBhcmVudC5cbiAqIFVzZSBgcGFyc2VEb2N1bWVudGAgdG8gZ2V0IHRoZSBgRG9jdW1lbnRgIG5vZGUgaW5zdGVhZC5cbiAqXG4gKiBAcGFyYW0gZGF0YSBUaGUgZGF0YSB0aGF0IHNob3VsZCBiZSBwYXJzZWQuXG4gKiBAcGFyYW0gb3B0aW9ucyBPcHRpb25hbCBvcHRpb25zIGZvciB0aGUgcGFyc2VyIGFuZCBET00gYnVpbGRlci5cbiAqIEBkZXByZWNhdGVkIFVzZSBgcGFyc2VEb2N1bWVudGAgaW5zdGVhZC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhcnNlRE9NKGRhdGEsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gcGFyc2VEb2N1bWVudChkYXRhLCBvcHRpb25zKS5jaGlsZHJlbjtcbn1cbi8qKlxuICogQ3JlYXRlcyBhIHBhcnNlciBpbnN0YW5jZSwgd2l0aCBhbiBhdHRhY2hlZCBET00gaGFuZGxlci5cbiAqXG4gKiBAcGFyYW0gY2FsbGJhY2sgQSBjYWxsYmFjayB0aGF0IHdpbGwgYmUgY2FsbGVkIG9uY2UgcGFyc2luZyBoYXMgYmVlbiBjb21wbGV0ZWQuXG4gKiBAcGFyYW0gb3B0aW9ucyBPcHRpb25hbCBvcHRpb25zIGZvciB0aGUgcGFyc2VyIGFuZCBET00gYnVpbGRlci5cbiAqIEBwYXJhbSBlbGVtZW50Q2FsbGJhY2sgQW4gb3B0aW9uYWwgY2FsbGJhY2sgdGhhdCB3aWxsIGJlIGNhbGxlZCBldmVyeSB0aW1lIGEgdGFnIGhhcyBiZWVuIGNvbXBsZXRlZCBpbnNpZGUgb2YgdGhlIERPTS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZURvbVN0cmVhbShjYWxsYmFjaywgb3B0aW9ucywgZWxlbWVudENhbGxiYWNrKSB7XG4gICAgY29uc3QgaGFuZGxlciA9IG5ldyBEb21IYW5kbGVyKGNhbGxiYWNrLCBvcHRpb25zLCBlbGVtZW50Q2FsbGJhY2spO1xuICAgIHJldHVybiBuZXcgUGFyc2VyKGhhbmRsZXIsIG9wdGlvbnMpO1xufVxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUb2tlbml6ZXIsIH0gZnJvbSBcIi4vVG9rZW5pemVyLmpzXCI7XG4vKlxuICogQWxsIG9mIHRoZSBmb2xsb3dpbmcgZXhwb3J0cyBleGlzdCBmb3IgYmFja3dhcmRzLWNvbXBhdGliaWxpdHkuXG4gKiBUaGV5IHNob3VsZCBwcm9iYWJseSBiZSByZW1vdmVkIGV2ZW50dWFsbHkuXG4gKi9cbmV4cG9ydCAqIGFzIEVsZW1lbnRUeXBlIGZyb20gXCJkb21lbGVtZW50dHlwZVwiO1xuaW1wb3J0IHsgZ2V0RmVlZCB9IGZyb20gXCJkb211dGlsc1wiO1xuZXhwb3J0IHsgZ2V0RmVlZCB9IGZyb20gXCJkb211dGlsc1wiO1xuY29uc3QgcGFyc2VGZWVkRGVmYXVsdE9wdGlvbnMgPSB7IHhtbE1vZGU6IHRydWUgfTtcbi8qKlxuICogUGFyc2UgYSBmZWVkLlxuICpcbiAqIEBwYXJhbSBmZWVkIFRoZSBmZWVkIHRoYXQgc2hvdWxkIGJlIHBhcnNlZCwgYXMgYSBzdHJpbmcuXG4gKiBAcGFyYW0gb3B0aW9ucyBPcHRpb25hbGx5LCBvcHRpb25zIGZvciBwYXJzaW5nLiBXaGVuIHVzaW5nIHRoaXMsIHlvdSBzaG91bGQgc2V0IGB4bWxNb2RlYCB0byBgdHJ1ZWAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZUZlZWQoZmVlZCwgb3B0aW9ucyA9IHBhcnNlRmVlZERlZmF1bHRPcHRpb25zKSB7XG4gICAgcmV0dXJuIGdldEZlZWQocGFyc2VET00oZmVlZCwgb3B0aW9ucykpO1xufVxuZXhwb3J0ICogYXMgRG9tVXRpbHMgZnJvbSBcImRvbXV0aWxzXCI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/htmlparser2/lib/esm/index.js\n");

/***/ })

};
;