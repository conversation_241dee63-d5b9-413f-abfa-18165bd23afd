"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/domutils";
exports.ids = ["vendor-chunks/domutils"];
exports.modules = {

/***/ "(rsc)/./node_modules/domutils/lib/esm/feeds.js":
/*!************************************************!*\
  !*** ./node_modules/domutils/lib/esm/feeds.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFeed: () => (/* binding */ getFeed)\n/* harmony export */ });\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/domutils/lib/esm/stringify.js\");\n/* harmony import */ var _legacy_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./legacy.js */ \"(rsc)/./node_modules/domutils/lib/esm/legacy.js\");\n\n\n/**\n * Get the feed object from the root of a DOM tree.\n *\n * @category Feeds\n * @param doc - The DOM to to extract the feed from.\n * @returns The feed.\n */\nfunction getFeed(doc) {\n    const feedRoot = getOneElement(isValidFeed, doc);\n    return !feedRoot\n        ? null\n        : feedRoot.name === \"feed\"\n            ? getAtomFeed(feedRoot)\n            : getRssFeed(feedRoot);\n}\n/**\n * Parse an Atom feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getAtomFeed(feedRoot) {\n    var _a;\n    const childs = feedRoot.children;\n    const feed = {\n        type: \"atom\",\n        items: (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(\"entry\", childs).map((item) => {\n            var _a;\n            const { children } = item;\n            const entry = { media: getMediaElements(children) };\n            addConditionally(entry, \"id\", \"id\", children);\n            addConditionally(entry, \"title\", \"title\", children);\n            const href = (_a = getOneElement(\"link\", children)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n            if (href) {\n                entry.link = href;\n            }\n            const description = fetch(\"summary\", children) || fetch(\"content\", children);\n            if (description) {\n                entry.description = description;\n            }\n            const pubDate = fetch(\"updated\", children);\n            if (pubDate) {\n                entry.pubDate = new Date(pubDate);\n            }\n            return entry;\n        }),\n    };\n    addConditionally(feed, \"id\", \"id\", childs);\n    addConditionally(feed, \"title\", \"title\", childs);\n    const href = (_a = getOneElement(\"link\", childs)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n    if (href) {\n        feed.link = href;\n    }\n    addConditionally(feed, \"description\", \"subtitle\", childs);\n    const updated = fetch(\"updated\", childs);\n    if (updated) {\n        feed.updated = new Date(updated);\n    }\n    addConditionally(feed, \"author\", \"email\", childs, true);\n    return feed;\n}\n/**\n * Parse a RSS feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getRssFeed(feedRoot) {\n    var _a, _b;\n    const childs = (_b = (_a = getOneElement(\"channel\", feedRoot.children)) === null || _a === void 0 ? void 0 : _a.children) !== null && _b !== void 0 ? _b : [];\n    const feed = {\n        type: feedRoot.name.substr(0, 3),\n        id: \"\",\n        items: (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(\"item\", feedRoot.children).map((item) => {\n            const { children } = item;\n            const entry = { media: getMediaElements(children) };\n            addConditionally(entry, \"id\", \"guid\", children);\n            addConditionally(entry, \"title\", \"title\", children);\n            addConditionally(entry, \"link\", \"link\", children);\n            addConditionally(entry, \"description\", \"description\", children);\n            const pubDate = fetch(\"pubDate\", children) || fetch(\"dc:date\", children);\n            if (pubDate)\n                entry.pubDate = new Date(pubDate);\n            return entry;\n        }),\n    };\n    addConditionally(feed, \"title\", \"title\", childs);\n    addConditionally(feed, \"link\", \"link\", childs);\n    addConditionally(feed, \"description\", \"description\", childs);\n    const updated = fetch(\"lastBuildDate\", childs);\n    if (updated) {\n        feed.updated = new Date(updated);\n    }\n    addConditionally(feed, \"author\", \"managingEditor\", childs, true);\n    return feed;\n}\nconst MEDIA_KEYS_STRING = [\"url\", \"type\", \"lang\"];\nconst MEDIA_KEYS_INT = [\n    \"fileSize\",\n    \"bitrate\",\n    \"framerate\",\n    \"samplingrate\",\n    \"channels\",\n    \"duration\",\n    \"height\",\n    \"width\",\n];\n/**\n * Get all media elements of a feed item.\n *\n * @param where Nodes to search in.\n * @returns Media elements.\n */\nfunction getMediaElements(where) {\n    return (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(\"media:content\", where).map((elem) => {\n        const { attribs } = elem;\n        const media = {\n            medium: attribs[\"medium\"],\n            isDefault: !!attribs[\"isDefault\"],\n        };\n        for (const attrib of MEDIA_KEYS_STRING) {\n            if (attribs[attrib]) {\n                media[attrib] = attribs[attrib];\n            }\n        }\n        for (const attrib of MEDIA_KEYS_INT) {\n            if (attribs[attrib]) {\n                media[attrib] = parseInt(attribs[attrib], 10);\n            }\n        }\n        if (attribs[\"expression\"]) {\n            media.expression = attribs[\"expression\"];\n        }\n        return media;\n    });\n}\n/**\n * Get one element by tag name.\n *\n * @param tagName Tag name to look for\n * @param node Node to search in\n * @returns The element or null\n */\nfunction getOneElement(tagName, node) {\n    return (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(tagName, node, true, 1)[0];\n}\n/**\n * Get the text content of an element with a certain tag name.\n *\n * @param tagName Tag name to look for.\n * @param where Node to search in.\n * @param recurse Whether to recurse into child nodes.\n * @returns The text content of the element.\n */\nfunction fetch(tagName, where, recurse = false) {\n    return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_0__.textContent)((0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(tagName, where, recurse, 1)).trim();\n}\n/**\n * Adds a property to an object if it has a value.\n *\n * @param obj Object to be extended\n * @param prop Property name\n * @param tagName Tag name that contains the conditionally added property\n * @param where Element to search for the property\n * @param recurse Whether to recurse into child nodes.\n */\nfunction addConditionally(obj, prop, tagName, where, recurse = false) {\n    const val = fetch(tagName, where, recurse);\n    if (val)\n        obj[prop] = val;\n}\n/**\n * Checks if an element is a feed root node.\n *\n * @param value The name of the element to check.\n * @returns Whether an element is a feed root node.\n */\nfunction isValidFeed(value) {\n    return value === \"rss\" || value === \"feed\" || value === \"rdf:RDF\";\n}\n//# sourceMappingURL=feeds.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/feeds.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/helpers.js":
/*!**************************************************!*\
  !*** ./node_modules/domutils/lib/esm/helpers.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentPosition: () => (/* binding */ DocumentPosition),\n/* harmony export */   compareDocumentPosition: () => (/* binding */ compareDocumentPosition),\n/* harmony export */   removeSubsets: () => (/* binding */ removeSubsets),\n/* harmony export */   uniqueSort: () => (/* binding */ uniqueSort)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n/**\n * Given an array of nodes, remove any member that is contained by another\n * member.\n *\n * @category Helpers\n * @param nodes Nodes to filter.\n * @returns Remaining nodes that aren't contained by other nodes.\n */\nfunction removeSubsets(nodes) {\n    let idx = nodes.length;\n    /*\n     * Check if each node (or one of its ancestors) is already contained in the\n     * array.\n     */\n    while (--idx >= 0) {\n        const node = nodes[idx];\n        /*\n         * Remove the node if it is not unique.\n         * We are going through the array from the end, so we only\n         * have to check nodes that preceed the node under consideration in the array.\n         */\n        if (idx > 0 && nodes.lastIndexOf(node, idx - 1) >= 0) {\n            nodes.splice(idx, 1);\n            continue;\n        }\n        for (let ancestor = node.parent; ancestor; ancestor = ancestor.parent) {\n            if (nodes.includes(ancestor)) {\n                nodes.splice(idx, 1);\n                break;\n            }\n        }\n    }\n    return nodes;\n}\n/**\n * @category Helpers\n * @see {@link http://dom.spec.whatwg.org/#dom-node-comparedocumentposition}\n */\nvar DocumentPosition;\n(function (DocumentPosition) {\n    DocumentPosition[DocumentPosition[\"DISCONNECTED\"] = 1] = \"DISCONNECTED\";\n    DocumentPosition[DocumentPosition[\"PRECEDING\"] = 2] = \"PRECEDING\";\n    DocumentPosition[DocumentPosition[\"FOLLOWING\"] = 4] = \"FOLLOWING\";\n    DocumentPosition[DocumentPosition[\"CONTAINS\"] = 8] = \"CONTAINS\";\n    DocumentPosition[DocumentPosition[\"CONTAINED_BY\"] = 16] = \"CONTAINED_BY\";\n})(DocumentPosition || (DocumentPosition = {}));\n/**\n * Compare the position of one node against another node in any other document,\n * returning a bitmask with the values from {@link DocumentPosition}.\n *\n * Document order:\n * > There is an ordering, document order, defined on all the nodes in the\n * > document corresponding to the order in which the first character of the\n * > XML representation of each node occurs in the XML representation of the\n * > document after expansion of general entities. Thus, the document element\n * > node will be the first node. Element nodes occur before their children.\n * > Thus, document order orders element nodes in order of the occurrence of\n * > their start-tag in the XML (after expansion of entities). The attribute\n * > nodes of an element occur after the element and before its children. The\n * > relative order of attribute nodes is implementation-dependent.\n *\n * Source:\n * http://www.w3.org/TR/DOM-Level-3-Core/glossary.html#dt-document-order\n *\n * @category Helpers\n * @param nodeA The first node to use in the comparison\n * @param nodeB The second node to use in the comparison\n * @returns A bitmask describing the input nodes' relative position.\n *\n * See http://dom.spec.whatwg.org/#dom-node-comparedocumentposition for\n * a description of these values.\n */\nfunction compareDocumentPosition(nodeA, nodeB) {\n    const aParents = [];\n    const bParents = [];\n    if (nodeA === nodeB) {\n        return 0;\n    }\n    let current = (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(nodeA) ? nodeA : nodeA.parent;\n    while (current) {\n        aParents.unshift(current);\n        current = current.parent;\n    }\n    current = (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(nodeB) ? nodeB : nodeB.parent;\n    while (current) {\n        bParents.unshift(current);\n        current = current.parent;\n    }\n    const maxIdx = Math.min(aParents.length, bParents.length);\n    let idx = 0;\n    while (idx < maxIdx && aParents[idx] === bParents[idx]) {\n        idx++;\n    }\n    if (idx === 0) {\n        return DocumentPosition.DISCONNECTED;\n    }\n    const sharedParent = aParents[idx - 1];\n    const siblings = sharedParent.children;\n    const aSibling = aParents[idx];\n    const bSibling = bParents[idx];\n    if (siblings.indexOf(aSibling) > siblings.indexOf(bSibling)) {\n        if (sharedParent === nodeB) {\n            return DocumentPosition.FOLLOWING | DocumentPosition.CONTAINED_BY;\n        }\n        return DocumentPosition.FOLLOWING;\n    }\n    if (sharedParent === nodeA) {\n        return DocumentPosition.PRECEDING | DocumentPosition.CONTAINS;\n    }\n    return DocumentPosition.PRECEDING;\n}\n/**\n * Sort an array of nodes based on their relative position in the document,\n * removing any duplicate nodes. If the array contains nodes that do not belong\n * to the same document, sort order is unspecified.\n *\n * @category Helpers\n * @param nodes Array of DOM nodes.\n * @returns Collection of unique nodes, sorted in document order.\n */\nfunction uniqueSort(nodes) {\n    nodes = nodes.filter((node, i, arr) => !arr.includes(node, i + 1));\n    nodes.sort((a, b) => {\n        const relative = compareDocumentPosition(a, b);\n        if (relative & DocumentPosition.PRECEDING) {\n            return -1;\n        }\n        else if (relative & DocumentPosition.FOLLOWING) {\n            return 1;\n        }\n        return 0;\n    });\n    return nodes;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/index.js":
/*!************************************************!*\
  !*** ./node_modules/domutils/lib/esm/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentPosition: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.DocumentPosition),\n/* harmony export */   append: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.append),\n/* harmony export */   appendChild: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.appendChild),\n/* harmony export */   compareDocumentPosition: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.compareDocumentPosition),\n/* harmony export */   existsOne: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.existsOne),\n/* harmony export */   filter: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.filter),\n/* harmony export */   find: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.find),\n/* harmony export */   findAll: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.findAll),\n/* harmony export */   findOne: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.findOne),\n/* harmony export */   findOneChild: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.findOneChild),\n/* harmony export */   getAttributeValue: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getAttributeValue),\n/* harmony export */   getChildren: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getChildren),\n/* harmony export */   getElementById: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementById),\n/* harmony export */   getElements: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElements),\n/* harmony export */   getElementsByClassName: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementsByClassName),\n/* harmony export */   getElementsByTagName: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementsByTagName),\n/* harmony export */   getElementsByTagType: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementsByTagType),\n/* harmony export */   getFeed: () => (/* reexport safe */ _feeds_js__WEBPACK_IMPORTED_MODULE_6__.getFeed),\n/* harmony export */   getInnerHTML: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.getInnerHTML),\n/* harmony export */   getName: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getName),\n/* harmony export */   getOuterHTML: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.getOuterHTML),\n/* harmony export */   getParent: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getParent),\n/* harmony export */   getSiblings: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getSiblings),\n/* harmony export */   getText: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.getText),\n/* harmony export */   hasAttrib: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.hasAttrib),\n/* harmony export */   hasChildren: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.hasChildren),\n/* harmony export */   innerText: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.innerText),\n/* harmony export */   isCDATA: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isCDATA),\n/* harmony export */   isComment: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isComment),\n/* harmony export */   isDocument: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isDocument),\n/* harmony export */   isTag: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isTag),\n/* harmony export */   isText: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isText),\n/* harmony export */   nextElementSibling: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.nextElementSibling),\n/* harmony export */   prepend: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.prepend),\n/* harmony export */   prependChild: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.prependChild),\n/* harmony export */   prevElementSibling: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.prevElementSibling),\n/* harmony export */   removeElement: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.removeElement),\n/* harmony export */   removeSubsets: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.removeSubsets),\n/* harmony export */   replaceElement: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.replaceElement),\n/* harmony export */   testElement: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.testElement),\n/* harmony export */   textContent: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.textContent),\n/* harmony export */   uniqueSort: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.uniqueSort)\n/* harmony export */ });\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/domutils/lib/esm/stringify.js\");\n/* harmony import */ var _traversal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./traversal.js */ \"(rsc)/./node_modules/domutils/lib/esm/traversal.js\");\n/* harmony import */ var _manipulation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./manipulation.js */ \"(rsc)/./node_modules/domutils/lib/esm/manipulation.js\");\n/* harmony import */ var _querying_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./querying.js */ \"(rsc)/./node_modules/domutils/lib/esm/querying.js\");\n/* harmony import */ var _legacy_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./legacy.js */ \"(rsc)/./node_modules/domutils/lib/esm/legacy.js\");\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./helpers.js */ \"(rsc)/./node_modules/domutils/lib/esm/helpers.js\");\n/* harmony import */ var _feeds_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./feeds.js */ \"(rsc)/./node_modules/domutils/lib/esm/feeds.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n\n\n\n\n\n\n/** @deprecated Use these methods from `domhandler` directly. */\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZG9tdXRpbHMvbGliL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUErQjtBQUNBO0FBQ0c7QUFDSjtBQUNGO0FBQ0M7QUFDRjtBQUMzQjtBQUN5RjtBQUN6RiIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9ub2RlX21vZHVsZXMvZG9tdXRpbHMvbGliL2VzbS9pbmRleC5qcz9mYmEzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL3N0cmluZ2lmeS5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHJhdmVyc2FsLmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9tYW5pcHVsYXRpb24uanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3F1ZXJ5aW5nLmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9sZWdhY3kuanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2hlbHBlcnMuanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2ZlZWRzLmpzXCI7XG4vKiogQGRlcHJlY2F0ZWQgVXNlIHRoZXNlIG1ldGhvZHMgZnJvbSBgZG9taGFuZGxlcmAgZGlyZWN0bHkuICovXG5leHBvcnQgeyBpc1RhZywgaXNDREFUQSwgaXNUZXh0LCBpc0NvbW1lbnQsIGlzRG9jdW1lbnQsIGhhc0NoaWxkcmVuLCB9IGZyb20gXCJkb21oYW5kbGVyXCI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/legacy.js":
/*!*************************************************!*\
  !*** ./node_modules/domutils/lib/esm/legacy.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getElementById: () => (/* binding */ getElementById),\n/* harmony export */   getElements: () => (/* binding */ getElements),\n/* harmony export */   getElementsByClassName: () => (/* binding */ getElementsByClassName),\n/* harmony export */   getElementsByTagName: () => (/* binding */ getElementsByTagName),\n/* harmony export */   getElementsByTagType: () => (/* binding */ getElementsByTagType),\n/* harmony export */   testElement: () => (/* binding */ testElement)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var _querying_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./querying.js */ \"(rsc)/./node_modules/domutils/lib/esm/querying.js\");\n\n\n/**\n * A map of functions to check nodes against.\n */\nconst Checks = {\n    tag_name(name) {\n        if (typeof name === \"function\") {\n            return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && name(elem.name);\n        }\n        else if (name === \"*\") {\n            return domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag;\n        }\n        return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && elem.name === name;\n    },\n    tag_type(type) {\n        if (typeof type === \"function\") {\n            return (elem) => type(elem.type);\n        }\n        return (elem) => elem.type === type;\n    },\n    tag_contains(data) {\n        if (typeof data === \"function\") {\n            return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(elem) && data(elem.data);\n        }\n        return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(elem) && elem.data === data;\n    },\n};\n/**\n * Returns a function to check whether a node has an attribute with a particular\n * value.\n *\n * @param attrib Attribute to check.\n * @param value Attribute value to look for.\n * @returns A function to check whether the a node has an attribute with a\n *   particular value.\n */\nfunction getAttribCheck(attrib, value) {\n    if (typeof value === \"function\") {\n        return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && value(elem.attribs[attrib]);\n    }\n    return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && elem.attribs[attrib] === value;\n}\n/**\n * Returns a function that returns `true` if either of the input functions\n * returns `true` for a node.\n *\n * @param a First function to combine.\n * @param b Second function to combine.\n * @returns A function taking a node and returning `true` if either of the input\n *   functions returns `true` for the node.\n */\nfunction combineFuncs(a, b) {\n    return (elem) => a(elem) || b(elem);\n}\n/**\n * Returns a function that executes all checks in `options` and returns `true`\n * if any of them match a node.\n *\n * @param options An object describing nodes to look for.\n * @returns A function that executes all checks in `options` and returns `true`\n *   if any of them match a node.\n */\nfunction compileTest(options) {\n    const funcs = Object.keys(options).map((key) => {\n        const value = options[key];\n        return Object.prototype.hasOwnProperty.call(Checks, key)\n            ? Checks[key](value)\n            : getAttribCheck(key, value);\n    });\n    return funcs.length === 0 ? null : funcs.reduce(combineFuncs);\n}\n/**\n * Checks whether a node matches the description in `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param node The element to test.\n * @returns Whether the element matches the description in `options`.\n */\nfunction testElement(options, node) {\n    const test = compileTest(options);\n    return test ? test(node) : true;\n}\n/**\n * Returns all nodes that match `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes that match `options`.\n */\nfunction getElements(options, nodes, recurse, limit = Infinity) {\n    const test = compileTest(options);\n    return test ? (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(test, nodes, recurse, limit) : [];\n}\n/**\n * Returns the node with the supplied ID.\n *\n * @category Legacy Query Functions\n * @param id The unique ID attribute value to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @returns The node with the supplied ID.\n */\nfunction getElementById(id, nodes, recurse = true) {\n    if (!Array.isArray(nodes))\n        nodes = [nodes];\n    return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.findOne)(getAttribCheck(\"id\", id), nodes, recurse);\n}\n/**\n * Returns all nodes with the supplied `tagName`.\n *\n * @category Legacy Query Functions\n * @param tagName Tag name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `tagName`.\n */\nfunction getElementsByTagName(tagName, nodes, recurse = true, limit = Infinity) {\n    return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(Checks[\"tag_name\"](tagName), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `className`.\n *\n * @category Legacy Query Functions\n * @param className Class name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `className`.\n */\nfunction getElementsByClassName(className, nodes, recurse = true, limit = Infinity) {\n    return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(getAttribCheck(\"class\", className), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `type`.\n *\n * @category Legacy Query Functions\n * @param type Element type to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `type`.\n */\nfunction getElementsByTagType(type, nodes, recurse = true, limit = Infinity) {\n    return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(Checks[\"tag_type\"](type), nodes, recurse, limit);\n}\n//# sourceMappingURL=legacy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/legacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/manipulation.js":
/*!*******************************************************!*\
  !*** ./node_modules/domutils/lib/esm/manipulation.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   append: () => (/* binding */ append),\n/* harmony export */   appendChild: () => (/* binding */ appendChild),\n/* harmony export */   prepend: () => (/* binding */ prepend),\n/* harmony export */   prependChild: () => (/* binding */ prependChild),\n/* harmony export */   removeElement: () => (/* binding */ removeElement),\n/* harmony export */   replaceElement: () => (/* binding */ replaceElement)\n/* harmony export */ });\n/**\n * Remove an element from the dom\n *\n * @category Manipulation\n * @param elem The element to be removed\n */\nfunction removeElement(elem) {\n    if (elem.prev)\n        elem.prev.next = elem.next;\n    if (elem.next)\n        elem.next.prev = elem.prev;\n    if (elem.parent) {\n        const childs = elem.parent.children;\n        const childsIndex = childs.lastIndexOf(elem);\n        if (childsIndex >= 0) {\n            childs.splice(childsIndex, 1);\n        }\n    }\n    elem.next = null;\n    elem.prev = null;\n    elem.parent = null;\n}\n/**\n * Replace an element in the dom\n *\n * @category Manipulation\n * @param elem The element to be replaced\n * @param replacement The element to be added\n */\nfunction replaceElement(elem, replacement) {\n    const prev = (replacement.prev = elem.prev);\n    if (prev) {\n        prev.next = replacement;\n    }\n    const next = (replacement.next = elem.next);\n    if (next) {\n        next.prev = replacement;\n    }\n    const parent = (replacement.parent = elem.parent);\n    if (parent) {\n        const childs = parent.children;\n        childs[childs.lastIndexOf(elem)] = replacement;\n        elem.parent = null;\n    }\n}\n/**\n * Append a child to an element.\n *\n * @category Manipulation\n * @param parent The element to append to.\n * @param child The element to be added as a child.\n */\nfunction appendChild(parent, child) {\n    removeElement(child);\n    child.next = null;\n    child.parent = parent;\n    if (parent.children.push(child) > 1) {\n        const sibling = parent.children[parent.children.length - 2];\n        sibling.next = child;\n        child.prev = sibling;\n    }\n    else {\n        child.prev = null;\n    }\n}\n/**\n * Append an element after another.\n *\n * @category Manipulation\n * @param elem The element to append after.\n * @param next The element be added.\n */\nfunction append(elem, next) {\n    removeElement(next);\n    const { parent } = elem;\n    const currNext = elem.next;\n    next.next = currNext;\n    next.prev = elem;\n    elem.next = next;\n    next.parent = parent;\n    if (currNext) {\n        currNext.prev = next;\n        if (parent) {\n            const childs = parent.children;\n            childs.splice(childs.lastIndexOf(currNext), 0, next);\n        }\n    }\n    else if (parent) {\n        parent.children.push(next);\n    }\n}\n/**\n * Prepend a child to an element.\n *\n * @category Manipulation\n * @param parent The element to prepend before.\n * @param child The element to be added as a child.\n */\nfunction prependChild(parent, child) {\n    removeElement(child);\n    child.parent = parent;\n    child.prev = null;\n    if (parent.children.unshift(child) !== 1) {\n        const sibling = parent.children[1];\n        sibling.prev = child;\n        child.next = sibling;\n    }\n    else {\n        child.next = null;\n    }\n}\n/**\n * Prepend an element before another.\n *\n * @category Manipulation\n * @param elem The element to prepend before.\n * @param prev The element be added.\n */\nfunction prepend(elem, prev) {\n    removeElement(prev);\n    const { parent } = elem;\n    if (parent) {\n        const childs = parent.children;\n        childs.splice(childs.indexOf(elem), 0, prev);\n    }\n    if (elem.prev) {\n        elem.prev.next = prev;\n    }\n    prev.parent = parent;\n    prev.prev = elem.prev;\n    prev.next = elem;\n    elem.prev = prev;\n}\n//# sourceMappingURL=manipulation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/manipulation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/querying.js":
/*!***************************************************!*\
  !*** ./node_modules/domutils/lib/esm/querying.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   existsOne: () => (/* binding */ existsOne),\n/* harmony export */   filter: () => (/* binding */ filter),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   findAll: () => (/* binding */ findAll),\n/* harmony export */   findOne: () => (/* binding */ findOne),\n/* harmony export */   findOneChild: () => (/* binding */ findOneChild)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n/**\n * Search a node and its children for nodes passing a test function. If `node` is not an array, it will be wrapped in one.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param node Node to search. Will be included in the result set if it matches.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction filter(test, node, recurse = true, limit = Infinity) {\n    return find(test, Array.isArray(node) ? node : [node], recurse, limit);\n}\n/**\n * Search an array of nodes and their children for nodes passing a test function.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction find(test, nodes, recurse, limit) {\n    const result = [];\n    /** Stack of the arrays we are looking at. */\n    const nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n    /** Stack of the indices within the arrays. */\n    const indexStack = [0];\n    for (;;) {\n        // First, check if the current array has any more elements to look at.\n        if (indexStack[0] >= nodeStack[0].length) {\n            // If we have no more arrays to look at, we are done.\n            if (indexStack.length === 1) {\n                return result;\n            }\n            // Otherwise, remove the current array from the stack.\n            nodeStack.shift();\n            indexStack.shift();\n            // Loop back to the start to continue with the next array.\n            continue;\n        }\n        const elem = nodeStack[0][indexStack[0]++];\n        if (test(elem)) {\n            result.push(elem);\n            if (--limit <= 0)\n                return result;\n        }\n        if (recurse && (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) && elem.children.length > 0) {\n            /*\n             * Add the children to the stack. We are depth-first, so this is\n             * the next array we look at.\n             */\n            indexStack.unshift(0);\n            nodeStack.unshift(elem.children);\n        }\n    }\n}\n/**\n * Finds the first element inside of an array that matches a test function. This is an alias for `Array.prototype.find`.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns The first node in the array that passes `test`.\n * @deprecated Use `Array.prototype.find` directly.\n */\nfunction findOneChild(test, nodes) {\n    return nodes.find(test);\n}\n/**\n * Finds one element in a tree that passes a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Node or array of nodes to search.\n * @param recurse Also consider child nodes.\n * @returns The first node that passes `test`.\n */\nfunction findOne(test, nodes, recurse = true) {\n    const searchedNodes = Array.isArray(nodes) ? nodes : [nodes];\n    for (let i = 0; i < searchedNodes.length; i++) {\n        const node = searchedNodes[i];\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(node) && test(node)) {\n            return node;\n        }\n        if (recurse && (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && node.children.length > 0) {\n            const found = findOne(test, node.children, true);\n            if (found)\n                return found;\n        }\n    }\n    return null;\n}\n/**\n * Checks if a tree of nodes contains at least one node passing a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns Whether a tree of nodes contains at least one node passing the test.\n */\nfunction existsOne(test, nodes) {\n    return (Array.isArray(nodes) ? nodes : [nodes]).some((node) => ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(node) && test(node)) ||\n        ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && existsOne(test, node.children)));\n}\n/**\n * Search an array of nodes and their children for elements passing a test function.\n *\n * Same as `find`, but limited to elements and with less options, leading to reduced complexity.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns All nodes passing `test`.\n */\nfunction findAll(test, nodes) {\n    const result = [];\n    const nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n    const indexStack = [0];\n    for (;;) {\n        if (indexStack[0] >= nodeStack[0].length) {\n            if (nodeStack.length === 1) {\n                return result;\n            }\n            // Otherwise, remove the current array from the stack.\n            nodeStack.shift();\n            indexStack.shift();\n            // Loop back to the start to continue with the next array.\n            continue;\n        }\n        const elem = nodeStack[0][indexStack[0]++];\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && test(elem))\n            result.push(elem);\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) && elem.children.length > 0) {\n            indexStack.unshift(0);\n            nodeStack.unshift(elem.children);\n        }\n    }\n}\n//# sourceMappingURL=querying.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/querying.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/stringify.js":
/*!****************************************************!*\
  !*** ./node_modules/domutils/lib/esm/stringify.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getInnerHTML: () => (/* binding */ getInnerHTML),\n/* harmony export */   getOuterHTML: () => (/* binding */ getOuterHTML),\n/* harmony export */   getText: () => (/* binding */ getText),\n/* harmony export */   innerText: () => (/* binding */ innerText),\n/* harmony export */   textContent: () => (/* binding */ textContent)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var dom_serializer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dom-serializer */ \"(rsc)/./node_modules/dom-serializer/lib/esm/index.js\");\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n\n\n\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the outer HTML of.\n * @param options Options for serialization.\n * @returns `node`'s outer HTML.\n */\nfunction getOuterHTML(node, options) {\n    return (0,dom_serializer__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node, options);\n}\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the inner HTML of.\n * @param options Options for serialization.\n * @returns `node`'s inner HTML.\n */\nfunction getInnerHTML(node, options) {\n    return (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node)\n        ? node.children.map((node) => getOuterHTML(node, options)).join(\"\")\n        : \"\";\n}\n/**\n * Get a node's inner text. Same as `textContent`, but inserts newlines for `<br>` tags. Ignores comments.\n *\n * @category Stringify\n * @deprecated Use `textContent` instead.\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n */\nfunction getText(node) {\n    if (Array.isArray(node))\n        return node.map(getText).join(\"\");\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(node))\n        return node.name === \"br\" ? \"\\n\" : getText(node.children);\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isCDATA)(node))\n        return getText(node.children);\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(node))\n        return node.data;\n    return \"\";\n}\n/**\n * Get a node's text content. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the text content of.\n * @returns `node`'s text content.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/textContent}\n */\nfunction textContent(node) {\n    if (Array.isArray(node))\n        return node.map(textContent).join(\"\");\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isComment)(node)) {\n        return textContent(node.children);\n    }\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(node))\n        return node.data;\n    return \"\";\n}\n/**\n * Get a node's inner text, ignoring `<script>` and `<style>` tags. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/innerText}\n */\nfunction innerText(node) {\n    if (Array.isArray(node))\n        return node.map(innerText).join(\"\");\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && (node.type === domelementtype__WEBPACK_IMPORTED_MODULE_2__.ElementType.Tag || (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isCDATA)(node))) {\n        return innerText(node.children);\n    }\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(node))\n        return node.data;\n    return \"\";\n}\n//# sourceMappingURL=stringify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/stringify.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/traversal.js":
/*!****************************************************!*\
  !*** ./node_modules/domutils/lib/esm/traversal.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAttributeValue: () => (/* binding */ getAttributeValue),\n/* harmony export */   getChildren: () => (/* binding */ getChildren),\n/* harmony export */   getName: () => (/* binding */ getName),\n/* harmony export */   getParent: () => (/* binding */ getParent),\n/* harmony export */   getSiblings: () => (/* binding */ getSiblings),\n/* harmony export */   hasAttrib: () => (/* binding */ hasAttrib),\n/* harmony export */   nextElementSibling: () => (/* binding */ nextElementSibling),\n/* harmony export */   prevElementSibling: () => (/* binding */ prevElementSibling)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n/**\n * Get a node's children.\n *\n * @category Traversal\n * @param elem Node to get the children of.\n * @returns `elem`'s children, or an empty array.\n */\nfunction getChildren(elem) {\n    return (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) ? elem.children : [];\n}\n/**\n * Get a node's parent.\n *\n * @category Traversal\n * @param elem Node to get the parent of.\n * @returns `elem`'s parent node, or `null` if `elem` is a root node.\n */\nfunction getParent(elem) {\n    return elem.parent || null;\n}\n/**\n * Gets an elements siblings, including the element itself.\n *\n * Attempts to get the children through the element's parent first. If we don't\n * have a parent (the element is a root node), we walk the element's `prev` &\n * `next` to get all remaining nodes.\n *\n * @category Traversal\n * @param elem Element to get the siblings of.\n * @returns `elem`'s siblings, including `elem`.\n */\nfunction getSiblings(elem) {\n    const parent = getParent(elem);\n    if (parent != null)\n        return getChildren(parent);\n    const siblings = [elem];\n    let { prev, next } = elem;\n    while (prev != null) {\n        siblings.unshift(prev);\n        ({ prev } = prev);\n    }\n    while (next != null) {\n        siblings.push(next);\n        ({ next } = next);\n    }\n    return siblings;\n}\n/**\n * Gets an attribute from an element.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to retrieve.\n * @returns The element's attribute value, or `undefined`.\n */\nfunction getAttributeValue(elem, name) {\n    var _a;\n    return (_a = elem.attribs) === null || _a === void 0 ? void 0 : _a[name];\n}\n/**\n * Checks whether an element has an attribute.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to look for.\n * @returns Returns whether `elem` has the attribute `name`.\n */\nfunction hasAttrib(elem, name) {\n    return (elem.attribs != null &&\n        Object.prototype.hasOwnProperty.call(elem.attribs, name) &&\n        elem.attribs[name] != null);\n}\n/**\n * Get the tag name of an element.\n *\n * @category Traversal\n * @param elem The element to get the name for.\n * @returns The tag name of `elem`.\n */\nfunction getName(elem) {\n    return elem.name;\n}\n/**\n * Returns the next element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the next sibling of.\n * @returns `elem`'s next sibling that is a tag, or `null` if there is no next\n * sibling.\n */\nfunction nextElementSibling(elem) {\n    let { next } = elem;\n    while (next !== null && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(next))\n        ({ next } = next);\n    return next;\n}\n/**\n * Returns the previous element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the previous sibling of.\n * @returns `elem`'s previous sibling that is a tag, or `null` if there is no\n * previous sibling.\n */\nfunction prevElementSibling(elem) {\n    let { prev } = elem;\n    while (prev !== null && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(prev))\n        ({ prev } = prev);\n    return prev;\n}\n//# sourceMappingURL=traversal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/traversal.js\n");

/***/ })

};
;