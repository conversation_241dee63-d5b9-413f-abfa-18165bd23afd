/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@sendgrid";
exports.ids = ["vendor-chunks/@sendgrid"];
exports.modules = {

/***/ "(rsc)/./node_modules/@sendgrid/client/index.js":
/*!************************************************!*\
  !*** ./node_modules/@sendgrid/client/index.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst client = __webpack_require__(/*! ./src/client */ \"(rsc)/./node_modules/@sendgrid/client/src/client.js\");\nconst Client = __webpack_require__(/*! ./src/classes/client */ \"(rsc)/./node_modules/@sendgrid/client/src/classes/client.js\");\n\nmodule.exports = client;\nmodule.exports.Client = Client;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2NsaWVudC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixlQUFlLG1CQUFPLENBQUMseUVBQWM7QUFDckMsZUFBZSxtQkFBTyxDQUFDLHlGQUFzQjs7QUFFN0M7QUFDQSxxQkFBcUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibGFjay1ibG9nLy4vbm9kZV9tb2R1bGVzL0BzZW5kZ3JpZC9jbGllbnQvaW5kZXguanM/MjhlNiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmNvbnN0IGNsaWVudCA9IHJlcXVpcmUoJy4vc3JjL2NsaWVudCcpO1xuY29uc3QgQ2xpZW50ID0gcmVxdWlyZSgnLi9zcmMvY2xhc3Nlcy9jbGllbnQnKTtcblxubW9kdWxlLmV4cG9ydHMgPSBjbGllbnQ7XG5tb2R1bGUuZXhwb3J0cy5DbGllbnQgPSBDbGllbnQ7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/client/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/client/src/classes/client.js":
/*!*************************************************************!*\
  !*** ./node_modules/@sendgrid/client/src/classes/client.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nconst axios = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/dist/node/axios.cjs\");\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/@sendgrid/client/package.json\");\nconst {\n  helpers: {\n    mergeData,\n  },\n  classes: {\n    Response,\n    ResponseError,\n  },\n} = __webpack_require__(/*! @sendgrid/helpers */ \"(rsc)/./node_modules/@sendgrid/helpers/index.js\");\n\nconst API_KEY_PREFIX = 'SG.';\nconst SENDGRID_BASE_URL = 'https://api.sendgrid.com/';\nconst TWILIO_BASE_URL = 'https://email.twilio.com/';\nconst SENDGRID_REGION = 'global';\n// Initialize the allowed regions and their corresponding hosts\nconst REGION_HOST_MAP = {\n  eu: 'https://api.eu.sendgrid.com/',\n  global: 'https://api.sendgrid.com/',\n};\nclass Client {\n  constructor() {\n    this.auth = '';\n    this.impersonateSubuser = '';\n    this.sendgrid_region = SENDGRID_REGION;\n\n    this.defaultHeaders = {\n      Accept: 'application/json',\n      'Content-Type': 'application/json',\n      'User-Agent': 'sendgrid/' + pkg.version + ';nodejs',\n    };\n\n    this.defaultRequest = {\n      baseUrl: SENDGRID_BASE_URL,\n      url: '',\n      method: 'GET',\n      headers: {},\n      maxContentLength: Infinity, // Don't limit the content length.\n      maxBodyLength: Infinity,\n    };\n  }\n\n  setApiKey(apiKey) {\n    this.auth = 'Bearer ' + apiKey;\n    this.setDefaultRequest('baseUrl', REGION_HOST_MAP[this.sendgrid_region]);\n\n    if (!this.isValidApiKey(apiKey)) {\n      console.warn(`API key does not start with \"${API_KEY_PREFIX}\".`);\n    }\n  }\n\n  setTwilioEmailAuth(username, password) {\n    const b64Auth = Buffer.from(username + ':' + password).toString('base64');\n    this.auth = 'Basic ' + b64Auth;\n    this.setDefaultRequest('baseUrl', TWILIO_BASE_URL);\n\n    if (!this.isValidTwilioAuth(username, password)) {\n      console.warn('Twilio Email credentials must be non-empty strings.');\n    }\n  }\n\n  isValidApiKey(apiKey) {\n    return this.isString(apiKey) && apiKey.trim().startsWith(API_KEY_PREFIX);\n  }\n\n  isValidTwilioAuth(username, password) {\n    return this.isString(username) && username\n      && this.isString(password) && password;\n  }\n\n  isString(value) {\n    return typeof value === 'string' || value instanceof String;\n  }\n\n  setImpersonateSubuser(subuser) {\n    this.impersonateSubuser = subuser;\n  }\n\n  setDefaultHeader(key, value) {\n    if (key !== null && typeof key === 'object') {\n      // key is an object\n      Object.assign(this.defaultHeaders, key);\n      return this;\n    }\n\n    this.defaultHeaders[key] = value;\n    return this;\n  }\n\n  setDefaultRequest(key, value) {\n    if (key !== null && typeof key === 'object') {\n      // key is an object\n      Object.assign(this.defaultRequest, key);\n      return this;\n    }\n\n    this.defaultRequest[key] = value;\n    return this;\n  }\n\n  /**\n   * Global is the default residency (or region)\n   * Global region means the message will be sent through https://api.sendgrid.com\n   * EU region means the message will be sent through https://api.eu.sendgrid.com\n   **/\n  setDataResidency(region) {\n    if (!REGION_HOST_MAP.hasOwnProperty(region)) {\n      console.warn('Region can only be \"global\" or \"eu\".');\n    } else {\n      this.sendgrid_region = region;\n      this.setDefaultRequest('baseUrl', REGION_HOST_MAP[region]);\n    }\n    return this;\n  }\n\n  createHeaders(data) {\n    // Merge data with default headers.\n    const headers = mergeData(this.defaultHeaders, data);\n\n    // Add auth, but don't overwrite if header already set.\n    if (typeof headers.Authorization === 'undefined' && this.auth) {\n      headers.Authorization = this.auth;\n    }\n\n    if (this.impersonateSubuser) {\n      headers['On-Behalf-Of'] = this.impersonateSubuser;\n    }\n\n    return headers;\n  }\n\n  createRequest(data) {\n    let options = {\n      url: data.uri || data.url,\n      baseUrl: data.baseUrl,\n      method: data.method,\n      data: data.body,\n      params: data.qs,\n      headers: data.headers,\n    };\n\n    // Merge data with default request.\n    options = mergeData(this.defaultRequest, options);\n    options.headers = this.createHeaders(options.headers);\n    options.baseURL = options.baseUrl;\n    delete options.baseUrl;\n\n    return options;\n  }\n\n  request(data, cb) {\n    data = this.createRequest(data);\n\n    const promise = new Promise((resolve, reject) => {\n      axios(data)\n        .then(response => {\n          return resolve([\n            new Response(response.status, response.data, response.headers),\n            response.data,\n          ]);\n        })\n        .catch(error => {\n          if (error.response) {\n            if (error.response.status >= 400) {\n              return reject(new ResponseError(error.response));\n            }\n          }\n          return reject(error);\n        });\n    });\n\n    // Throw an error in case a callback function was not passed.\n    if (cb && typeof cb !== 'function') {\n      throw new Error('Callback passed is not a function.');\n    }\n\n    if (cb) {\n      return promise\n        .then(result => cb(null, result))\n        .catch(error => cb(error, null));\n    }\n\n    return promise;\n  }\n}\n\nmodule.exports = Client;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/client/src/classes/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/client/src/client.js":
/*!*****************************************************!*\
  !*** ./node_modules/@sendgrid/client/src/client.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Dependencies\n */\nconst Client = __webpack_require__(/*! ./classes/client */ \"(rsc)/./node_modules/@sendgrid/client/src/classes/client.js\");\n\n//Export singleton instance\nmodule.exports = new Client();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2NsaWVudC9zcmMvY2xpZW50LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyxxRkFBa0I7O0FBRXpDO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibGFjay1ibG9nLy4vbm9kZV9tb2R1bGVzL0BzZW5kZ3JpZC9jbGllbnQvc3JjL2NsaWVudC5qcz8xNmY4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLyoqXG4gKiBEZXBlbmRlbmNpZXNcbiAqL1xuY29uc3QgQ2xpZW50ID0gcmVxdWlyZSgnLi9jbGFzc2VzL2NsaWVudCcpO1xuXG4vL0V4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2Vcbm1vZHVsZS5leHBvcnRzID0gbmV3IENsaWVudCgpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/client/src/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/classes/attachment.js":
/*!**************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/classes/attachment.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Dependencies\n */\nconst toCamelCase = __webpack_require__(/*! ../helpers/to-camel-case */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/to-camel-case.js\");\nconst toSnakeCase = __webpack_require__(/*! ../helpers/to-snake-case */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/to-snake-case.js\");\nconst deepClone = __webpack_require__(/*! ../helpers/deep-clone */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/deep-clone.js\");\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst path = __webpack_require__(/*! path */ \"path\");\n\n/**\n * Attachment class\n */\nclass Attachment {\n\n  /**\n   * Constructor\n   */\n  constructor(data) {\n\n    //Create from data\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * From data\n   */\n  fromData(data) {\n\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object for Mail data');\n    }\n\n    //Convert to camel case to make it workable, making a copy to prevent\n    //changes to the original objects\n    data = deepClone(data);\n    data = toCamelCase(data);\n\n    //Extract properties from data\n    const {\n      content,\n      filename,\n      type,\n      disposition,\n      contentId,\n      filePath,\n    } = data;\n\n    if ((typeof content !== 'undefined') && (typeof filePath !== 'undefined')) {\n      throw new Error(\n        \"The props 'content' and 'filePath' cannot be used together.\"\n      );\n    }\n\n    //Set data\n    this.setFilename(filename);\n    this.setType(type);\n    this.setDisposition(disposition);\n    this.setContentId(contentId);\n    this.setContent(filePath ? this.readFile(filePath) : content);\n  }\n\n  /**\n   * Read a file and return its content as base64\n   */\n  readFile(filePath) {\n    return fs.readFileSync(path.resolve(filePath));\n  }\n\n  /**\n   * Set content\n   */\n  setContent(content) {\n    //Duck type check toString on content if it's a Buffer as that's the method that will be called.\n    if (typeof content === 'string') {\n      this.content = content;\n      return;\n    } else if (content instanceof Buffer && content.toString !== undefined) {\n      this.content = content.toString();\n\n      if (this.disposition === 'attachment') {\n        this.content = content.toString('base64');\n      }\n\n      return;\n    }\n\n    throw new Error('`content` expected to be either Buffer or string');\n  }\n\n  /**\n   * Set content\n   */\n  setFileContent(content) {\n    if (content instanceof Buffer && content.toString !== undefined) {\n      this.content = content.toString('base64');\n      return;\n    }\n\n    throw new Error('`content` expected to be Buffer');\n  }\n\n  /**\n   * Set filename\n   */\n  setFilename(filename) {\n    if (typeof filename === 'undefined') {\n      return;\n    }\n    if (filename && typeof filename !== 'string') {\n      throw new Error('String expected for `filename`');\n    }\n    this.filename = filename;\n  }\n\n  /**\n   * Set type\n   */\n  setType(type) {\n    if (typeof type === 'undefined') {\n      return;\n    }\n    if (typeof type !== 'string') {\n      throw new Error('String expected for `type`');\n    }\n    this.type = type;\n  }\n\n  /**\n   * Set disposition\n   */\n  setDisposition(disposition) {\n    if (typeof disposition === 'undefined') {\n      return;\n    }\n    if (typeof disposition !== 'string') {\n      throw new Error('String expected for `disposition`');\n    }\n    this.disposition = disposition;\n  }\n\n  /**\n   * Set content ID\n   */\n  setContentId(contentId) {\n    if (typeof contentId === 'undefined') {\n      return;\n    }\n    if (typeof contentId !== 'string') {\n      throw new Error('String expected for `contentId`');\n    }\n    this.contentId = contentId;\n  }\n\n  /**\n   * To JSON\n   */\n  toJSON() {\n\n    //Extract properties from self\n    const {content, filename, type, disposition, contentId} = this;\n\n    //Initialize with mandatory properties\n    const json = {content, filename};\n\n    //Add whatever else we have\n    if (typeof type !== 'undefined') {\n      json.type = type;\n    }\n    if (typeof disposition !== 'undefined') {\n      json.disposition = disposition;\n    }\n    if (typeof contentId !== 'undefined') {\n      json.contentId = contentId;\n    }\n\n    //Return\n    return toSnakeCase(json);\n  }\n}\n\n//Export class\nmodule.exports = Attachment;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/classes/attachment.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/classes/email-address.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/classes/email-address.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Dependencies\n */\nconst splitNameEmail = __webpack_require__(/*! ../helpers/split-name-email */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/split-name-email.js\");\n\n/**\n * Email address class\n */\nclass EmailAddress {\n\n  /**\n\t * Constructor\n\t */\n  constructor(data) {\n\n    //Construct from data\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * From data\n   */\n  fromData(data) {\n\n    //String given\n    if (typeof data === 'string') {\n      const [name, email] = splitNameEmail(data);\n      data = {name, email};\n    }\n\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object or string for EmailAddress data');\n    }\n\n    //Extract name and email\n    const {name, email} = data;\n\n    //Set\n    this.setEmail(email);\n    this.setName(name);\n  }\n\n  /**\n   * Set name\n   */\n  setName(name) {\n    if (typeof name === 'undefined') {\n      return;\n    }\n    if (typeof name !== 'string') {\n      throw new Error('String expected for `name`');\n    }\n    this.name = name;\n  }\n\n  /**\n   * Set email (mandatory)\n   */\n  setEmail(email) {\n    if (typeof email === 'undefined') {\n      throw new Error('Must provide `email`');\n    }\n    if (typeof email !== 'string') {\n      throw new Error('String expected for `email`');\n    }\n    this.email = email;\n  }\n\n  /**\n\t * To JSON\n\t */\n  toJSON() {\n\n    //Get properties\n    const {email, name} = this;\n\n    //Initialize with mandatory properties\n    const json = {email};\n\n    //Add name if present\n    if (name !== '') {\n      json.name = name;\n    }\n\n    //Return\n    return json;\n  }\n\n  /**************************************************************************\n   * Static helpers\n   ***/\n\n  /**\n   * Create an EmailAddress instance from given data\n   */\n  static create(data) {\n\n    //Array?\n    if (Array.isArray(data)) {\n      return data\n        .filter(item => !!item)\n        .map(item => this.create(item));\n    }\n\n    //Already instance of EmailAddress class?\n    if (data instanceof EmailAddress) {\n      return data;\n    }\n\n    //Create instance\n    return new EmailAddress(data);\n  }\n}\n\n//Export class\nmodule.exports = EmailAddress;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/classes/email-address.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/classes/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/classes/index.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Expose classes\n */\nconst Attachment = __webpack_require__(/*! ./attachment */ \"(rsc)/./node_modules/@sendgrid/helpers/classes/attachment.js\");\nconst EmailAddress = __webpack_require__(/*! ./email-address */ \"(rsc)/./node_modules/@sendgrid/helpers/classes/email-address.js\");\nconst Mail = __webpack_require__(/*! ./mail */ \"(rsc)/./node_modules/@sendgrid/helpers/classes/mail.js\");\nconst Personalization = __webpack_require__(/*! ./personalization */ \"(rsc)/./node_modules/@sendgrid/helpers/classes/personalization.js\");\nconst Response = __webpack_require__(/*! ./response */ \"(rsc)/./node_modules/@sendgrid/helpers/classes/response.js\");\nconst ResponseError = __webpack_require__(/*! ./response-error */ \"(rsc)/./node_modules/@sendgrid/helpers/classes/response-error.js\");\nconst Statistics = __webpack_require__(/*! ./statistics */ \"(rsc)/./node_modules/@sendgrid/helpers/classes/statistics.js\");\n\n/**\n * Export\n */\nmodule.exports = {\n  Attachment,\n  EmailAddress,\n  Mail,\n  Personalization,\n  Response,\n  ResponseError,\n  Statistics,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvY2xhc3Nlcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsbUJBQU8sQ0FBQyxrRkFBYztBQUN6QyxxQkFBcUIsbUJBQU8sQ0FBQyx3RkFBaUI7QUFDOUMsYUFBYSxtQkFBTyxDQUFDLHNFQUFRO0FBQzdCLHdCQUF3QixtQkFBTyxDQUFDLDRGQUFtQjtBQUNuRCxpQkFBaUIsbUJBQU8sQ0FBQyw4RUFBWTtBQUNyQyxzQkFBc0IsbUJBQU8sQ0FBQywwRkFBa0I7QUFDaEQsbUJBQW1CLG1CQUFPLENBQUMsa0ZBQWM7O0FBRXpDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvY2xhc3Nlcy9pbmRleC5qcz8wNmZjIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLyoqXG4gKiBFeHBvc2UgY2xhc3Nlc1xuICovXG5jb25zdCBBdHRhY2htZW50ID0gcmVxdWlyZSgnLi9hdHRhY2htZW50Jyk7XG5jb25zdCBFbWFpbEFkZHJlc3MgPSByZXF1aXJlKCcuL2VtYWlsLWFkZHJlc3MnKTtcbmNvbnN0IE1haWwgPSByZXF1aXJlKCcuL21haWwnKTtcbmNvbnN0IFBlcnNvbmFsaXphdGlvbiA9IHJlcXVpcmUoJy4vcGVyc29uYWxpemF0aW9uJyk7XG5jb25zdCBSZXNwb25zZSA9IHJlcXVpcmUoJy4vcmVzcG9uc2UnKTtcbmNvbnN0IFJlc3BvbnNlRXJyb3IgPSByZXF1aXJlKCcuL3Jlc3BvbnNlLWVycm9yJyk7XG5jb25zdCBTdGF0aXN0aWNzID0gcmVxdWlyZSgnLi9zdGF0aXN0aWNzJyk7XG5cbi8qKlxuICogRXhwb3J0XG4gKi9cbm1vZHVsZS5leHBvcnRzID0ge1xuICBBdHRhY2htZW50LFxuICBFbWFpbEFkZHJlc3MsXG4gIE1haWwsXG4gIFBlcnNvbmFsaXphdGlvbixcbiAgUmVzcG9uc2UsXG4gIFJlc3BvbnNlRXJyb3IsXG4gIFN0YXRpc3RpY3MsXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/classes/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/classes/mail.js":
/*!********************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/classes/mail.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Dependencies\n */\nconst EmailAddress = __webpack_require__(/*! ./email-address */ \"(rsc)/./node_modules/@sendgrid/helpers/classes/email-address.js\");\nconst Personalization = __webpack_require__(/*! ./personalization */ \"(rsc)/./node_modules/@sendgrid/helpers/classes/personalization.js\");\nconst toCamelCase = __webpack_require__(/*! ../helpers/to-camel-case */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/to-camel-case.js\");\nconst toSnakeCase = __webpack_require__(/*! ../helpers/to-snake-case */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/to-snake-case.js\");\nconst deepClone = __webpack_require__(/*! ../helpers/deep-clone */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/deep-clone.js\");\nconst arrayToJSON = __webpack_require__(/*! ../helpers/array-to-json */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/array-to-json.js\");\nconst { DYNAMIC_TEMPLATE_CHAR_WARNING } = __webpack_require__(/*! ../constants */ \"(rsc)/./node_modules/@sendgrid/helpers/constants/index.js\");\nconst {validateMailSettings, validateTrackingSettings} = __webpack_require__(/*! ../helpers/validate-settings */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/validate-settings.js\");\n\n/**\n * Mail class\n */\nclass Mail {\n\n  /**\n   * Constructor\n   */\n  constructor(data) {\n\n    //Initialize array and object properties\n    this.isDynamic = false;\n    this.hideWarnings = false;\n    this.personalizations = [];\n    this.attachments = [];\n    this.content = [];\n    this.categories = [];\n    this.headers = {};\n    this.sections = {};\n    this.customArgs = {};\n    this.trackingSettings = {};\n    this.mailSettings = {};\n    this.asm = {};\n\n    //Helper properties\n    this.substitutions = null;\n    this.substitutionWrappers = null;\n    this.dynamicTemplateData = null;\n\n    //Process data if given\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * Build from data\n   */\n  fromData(data) {\n\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object for Mail data');\n    }\n\n    //Convert to camel case to make it workable, making a copy to prevent\n    //changes to the original objects\n    data = deepClone(data);\n    data = toCamelCase(data, ['substitutions', 'dynamicTemplateData', 'customArgs', 'headers', 'sections']);\n\n    //Extract properties from data\n    const {\n      to, from, replyTo, cc, bcc, sendAt, subject, text, html, content,\n      templateId, personalizations, attachments, ipPoolName, batchId,\n      sections, headers, categories, category, customArgs, asm, mailSettings,\n      trackingSettings, substitutions, substitutionWrappers, dynamicTemplateData, isMultiple,\n      hideWarnings, replyToList,\n    } = data;\n\n    //Set data\n    this.setFrom(from);\n    this.setReplyTo(replyTo);\n    this.setSubject(subject);\n    this.setSendAt(sendAt);\n    this.setTemplateId(templateId);\n    this.setBatchId(batchId);\n    this.setIpPoolName(ipPoolName);\n    this.setAttachments(attachments);\n    this.setContent(content);\n    this.setSections(sections);\n    this.setHeaders(headers);\n    this.setCategories(category);\n    this.setCategories(categories);\n    this.setCustomArgs(customArgs);\n    this.setAsm(asm);\n    this.setMailSettings(mailSettings);\n    this.setTrackingSettings(trackingSettings);\n    this.setHideWarnings(hideWarnings);\n    this.setReplyToList(replyToList);\n\n    if (this.isDynamic) {\n      this.setDynamicTemplateData(dynamicTemplateData);\n    } else {\n      this.setSubstitutions(substitutions);\n      this.setSubstitutionWrappers(substitutionWrappers);\n    }\n\n    //Add contents from text/html properties\n    this.addTextContent(text);\n    this.addHtmlContent(html);\n\n    //Using \"to\" property for personalizations\n    if (personalizations) {\n      this.setPersonalizations(personalizations);\n    } else if (isMultiple && Array.isArray(to)) {\n      //Multiple individual emails\n      to.forEach(to => this.addTo(to, cc, bcc));\n    } else {\n      //Single email (possibly with multiple recipients in the to field)\n      this.addTo(to, cc, bcc);\n    }\n  }\n\n  /**\n   * Set from email\n   */\n  setFrom(from) {\n    if (this._checkProperty('from', from, [this._checkUndefined])) {\n      if (typeof from !== 'string' && typeof from.email !== 'string') {\n        throw new Error('String or address object expected for `from`');\n      }\n      this.from = EmailAddress.create(from);\n    }\n  }\n\n  /**\n   * Set reply to\n   */\n  setReplyTo(replyTo) {\n    if (this._checkProperty('replyTo', replyTo, [this._checkUndefined])) {\n      if (typeof replyTo !== 'string' && typeof replyTo.email !== 'string') {\n        throw new Error('String or address object expected for `replyTo`');\n      }\n      this.replyTo = EmailAddress.create(replyTo);\n    }\n  }\n\n  /**\n   * Set subject\n   */\n  setSubject(subject) {\n    this._setProperty('subject', subject, 'string');\n  }\n\n  /**\n   * Set send at\n   */\n  setSendAt(sendAt) {\n    if (this._checkProperty('sendAt', sendAt, [this._checkUndefined, this._createCheckThatThrows(Number.isInteger, 'Integer expected for `sendAt`')])) {\n      this.sendAt = sendAt;\n    }\n  }\n\n  /**\n   * Set template ID, also checks if the template is dynamic or legacy\n   */\n  setTemplateId(templateId) {\n    if (this._setProperty('templateId', templateId, 'string')) {\n      if (templateId.indexOf('d-') === 0) {\n        this.isDynamic = true;\n      }\n    }\n  }\n\n  /**\n   * Set batch ID\n   */\n  setBatchId(batchId) {\n    this._setProperty('batchId', batchId, 'string');\n  }\n\n  /**\n   * Set IP pool name\n   */\n  setIpPoolName(ipPoolName) {\n    this._setProperty('ipPoolName', ipPoolName, 'string');\n  }\n\n  /**\n   * Set ASM\n   */\n  setAsm(asm) {\n    if (this._checkProperty('asm', asm, [this._checkUndefined, this._createTypeCheck('object')])) {\n      if (typeof asm.groupId !== 'number') {\n        throw new Error('Expected `asm` to include an integer in its `groupId` field');\n      }\n      if (asm.groupsToDisplay &&\n        (!Array.isArray(asm.groupsToDisplay) || !asm.groupsToDisplay.every(group => typeof group === 'number'))) {\n        throw new Error('Array of integers expected for `asm.groupsToDisplay`');\n      }\n      this.asm = asm;\n    }\n  }\n\n  /**\n   * Set personalizations\n   */\n  setPersonalizations(personalizations) {\n    if (!this._doArrayCheck('personalizations', personalizations)) {\n      return;\n    }\n\n    if (!personalizations.every(personalization => typeof personalization === 'object')) {\n      throw new Error('Array of objects expected for `personalizations`');\n    }\n\n    //Clear and use add helper to add one by one\n    this.personalizations = [];\n    personalizations\n      .forEach(personalization => this.addPersonalization(personalization));\n  }\n\n  /**\n   * Add personalization\n   */\n  addPersonalization(personalization) {\n    //We should either send substitutions or dynamicTemplateData\n    //depending on the templateId\n    if (this.isDynamic && personalization.substitutions) {\n      delete personalization.substitutions;\n    } else if (!this.isDynamic && personalization.dynamicTemplateData) {\n      delete personalization.dynamicTemplateData;\n    }\n\n    //Convert to class if needed\n    if (!(personalization instanceof Personalization)) {\n      personalization = new Personalization(personalization);\n    }\n\n    //If this is dynamic, set dynamicTemplateData, or set substitutions\n    if (this.isDynamic) {\n      this.applyDynamicTemplateData(personalization);\n    } else {\n      this.applySubstitutions(personalization);\n    }\n\n    //Push personalization to array\n    this.personalizations.push(personalization);\n  }\n\n  /**\n   * Convenience method for quickly creating personalizations\n   */\n  addTo(to, cc, bcc) {\n    if (\n      typeof to === 'undefined' &&\n      typeof cc === 'undefined' &&\n      typeof bcc === 'undefined'\n    ) {\n      throw new Error('Provide at least one of to, cc or bcc');\n    }\n    this.addPersonalization(new Personalization({to, cc, bcc}));\n  }\n\n  /**\n   * Set substitutions\n   */\n  setSubstitutions(substitutions) {\n    this._setProperty('substitutions', substitutions, 'object');\n  }\n\n  /**\n   * Set substitution wrappers\n   */\n  setSubstitutionWrappers(substitutionWrappers) {\n    let lengthCheck = (propertyName, value) => {\n      if (!Array.isArray(value) || value.length !== 2) {\n        throw new Error('Array expected with two elements for `' + propertyName + '`');\n      }\n    };\n\n    if (this._checkProperty('substitutionWrappers', substitutionWrappers, [this._checkUndefined, lengthCheck])) {\n      this.substitutionWrappers = substitutionWrappers;\n    }\n  }\n\n  /**\n   * Helper which applies globally set substitutions to personalizations\n   */\n  applySubstitutions(personalization) {\n    if (personalization instanceof Personalization) {\n      personalization.reverseMergeSubstitutions(this.substitutions);\n      personalization.setSubstitutionWrappers(this.substitutionWrappers);\n    }\n  }\n\n  /**\n   * Helper which applies globally set dynamic_template_data to personalizations\n   */\n  applyDynamicTemplateData(personalization) {\n    if (personalization instanceof Personalization) {\n      personalization.deepMergeDynamicTemplateData(this.dynamicTemplateData);\n    }\n  }\n\n  /**\n   * Set dynamicTemplateData\n   */\n  setDynamicTemplateData(dynamicTemplateData) {\n    if (typeof dynamicTemplateData === 'undefined') {\n      return;\n    }\n    if (typeof dynamicTemplateData !== 'object') {\n      throw new Error('Object expected for `dynamicTemplateData`');\n    }\n\n    // Check dynamic template for non-escaped characters and warn if found\n    if (!this.hideWarnings) {\n      Object.values(dynamicTemplateData).forEach(value => {\n        if (/['\"&]/.test(value)) {\n          console.warn(DYNAMIC_TEMPLATE_CHAR_WARNING);\n        }\n      });\n    }\n\n    this.dynamicTemplateData = dynamicTemplateData;\n  }\n\n  /**\n   * Set content\n   */\n  setContent(content) {\n    if (this._doArrayCheck('content', content)) {\n      if (!content.every(contentField => typeof contentField === 'object')) {\n        throw new Error('Expected each entry in `content` to be an object');\n      }\n      if (!content.every(contentField => typeof contentField.type === 'string')) {\n        throw new Error('Expected each `content` entry to contain a `type` string');\n      }\n      if (!content.every(contentField => typeof contentField.value === 'string')) {\n        throw new Error('Expected each `content` entry to contain a `value` string');\n      }\n      this.content = content;\n    }\n  }\n\n  /**\n   * Add content\n   */\n  addContent(content) {\n    if (this._checkProperty('content', content, [this._createTypeCheck('object')])) {\n      this.content.push(content);\n    }\n  }\n\n  /**\n   * Add text content\n   */\n  addTextContent(text) {\n    if (this._checkProperty('text', text, [this._checkUndefined, this._createTypeCheck('string')])) {\n      this.addContent({\n        value: text,\n        type: 'text/plain',\n      });\n    }\n  }\n\n  /**\n   * Add HTML content\n   */\n  addHtmlContent(html) {\n    if (this._checkProperty('html', html, [this._checkUndefined, this._createTypeCheck('string')])) {\n      this.addContent({\n        value: html,\n        type: 'text/html',\n      });\n    }\n  }\n\n  /**\n   * Set attachments\n   */\n  setAttachments(attachments) {\n    if (this._doArrayCheck('attachments', attachments)) {\n      if (!attachments.every(attachment => typeof attachment.content === 'string')) {\n        throw new Error('Expected each attachment to contain a `content` string');\n      }\n      if (!attachments.every(attachment => typeof attachment.filename === 'string')) {\n        throw new Error('Expected each attachment to contain a `filename` string');\n      }\n      if (!attachments.every(attachment => !attachment.type || typeof attachment.type === 'string')) {\n        throw new Error('Expected the attachment\\'s `type` field to be a string');\n      }\n      if (!attachments.every(attachment => !attachment.disposition || typeof attachment.disposition === 'string')) {\n        throw new Error('Expected the attachment\\'s `disposition` field to be a string');\n      }\n      this.attachments = attachments;\n    }\n  }\n\n  /**\n   * Add attachment\n   */\n  addAttachment(attachment) {\n    if (this._checkProperty('attachment', attachment, [this._checkUndefined, this._createTypeCheck('object')])) {\n      this.attachments.push(attachment);\n    }\n  }\n\n  /**\n   * Set categories\n   */\n  setCategories(categories) {\n    let allElementsAreStrings = (propertyName, value) => {\n      if (!Array.isArray(value) || !value.every(item => typeof item === 'string')) {\n        throw new Error('Array of strings expected for `' + propertyName + '`');\n      }\n    };\n\n    if (typeof categories === 'string') {\n      categories = [categories];\n    }\n\n    if (this._checkProperty('categories', categories, [this._checkUndefined, allElementsAreStrings])) {\n      this.categories = categories;\n    }\n  }\n\n  /**\n   * Add category\n   */\n  addCategory(category) {\n    if (this._checkProperty('category', category, [this._createTypeCheck('string')])) {\n      this.categories.push(category);\n    }\n  }\n\n  /**\n   * Set headers\n   */\n  setHeaders(headers) {\n    this._setProperty('headers', headers, 'object');\n  }\n\n  /**\n   * Add a header\n   */\n  addHeader(key, value) {\n    if (this._checkProperty('key', key, [this._createTypeCheck('string')])\n      && this._checkProperty('value', value, [this._createTypeCheck('string')])) {\n      this.headers[key] = value;\n    }\n  }\n\n  /**\n   * Set sections\n   */\n  setSections(sections) {\n    this._setProperty('sections', sections, 'object');\n  }\n\n  /**\n   * Set custom args\n   */\n  setCustomArgs(customArgs) {\n    this._setProperty('customArgs', customArgs, 'object');\n  }\n\n  /**\n   * Set tracking settings\n   */\n  setTrackingSettings(settings) {\n    if (typeof settings === 'undefined') {\n      return;\n    }\n    validateTrackingSettings(settings);\n    this.trackingSettings = settings;\n  }\n\n  /**\n   * Set mail settings\n   */\n  setMailSettings(settings) {\n    if (typeof settings === 'undefined') {\n      return;\n    }\n    validateMailSettings(settings);\n    this.mailSettings = settings;\n  }\n\n  /**\n   * Set hide warnings\n   */\n  setHideWarnings(hide) {\n    if (typeof hide === 'undefined') {\n      return;\n    }\n    if (typeof hide !== 'boolean') {\n      throw new Error('Boolean expected for `hideWarnings`');\n    }\n    this.hideWarnings = hide;\n  }\n\n  /**\n   * To JSON\n   */\n  toJSON() {\n\n    //Extract properties from self\n    const {\n      from, replyTo, sendAt, subject, content, templateId,\n      personalizations, attachments, ipPoolName, batchId, asm,\n      sections, headers, categories, customArgs, mailSettings,\n      trackingSettings, replyToList,\n    } = this;\n\n    //Initialize with mandatory values\n    const json = {\n      from, subject,\n      personalizations: arrayToJSON(personalizations),\n    };\n\n    //Array properties\n    if (Array.isArray(attachments) && attachments.length > 0) {\n      json.attachments = arrayToJSON(attachments);\n    }\n    if (Array.isArray(categories) && categories.length > 0) {\n      json.categories = categories.filter(cat => cat !== '');\n    }\n    if (Array.isArray(content) && content.length > 0) {\n      json.content = arrayToJSON(content);\n    }\n\n    //Object properties\n    if (Object.keys(headers).length > 0) {\n      json.headers = headers;\n    }\n    if (Object.keys(mailSettings).length > 0) {\n      json.mailSettings = mailSettings;\n    }\n    if (Object.keys(trackingSettings).length > 0) {\n      json.trackingSettings = trackingSettings;\n    }\n    if (Object.keys(customArgs).length > 0) {\n      json.customArgs = customArgs;\n    }\n    if (Object.keys(sections).length > 0) {\n      json.sections = sections;\n    }\n    if (Object.keys(asm).length > 0) {\n      json.asm = asm;\n    }\n\n    //Simple properties\n    if (typeof replyTo !== 'undefined') {\n      json.replyTo = replyTo;\n    }\n    if (typeof sendAt !== 'undefined') {\n      json.sendAt = sendAt;\n    }\n    if (typeof batchId !== 'undefined') {\n      json.batchId = batchId;\n    }\n    if (typeof templateId !== 'undefined') {\n      json.templateId = templateId;\n    }\n    if (typeof ipPoolName !== 'undefined') {\n      json.ipPoolName = ipPoolName;\n    }\n    if(typeof replyToList !== 'undefined') {\n      json.replyToList = replyToList;\n    }\n\n    //Return as snake cased object\n    return toSnakeCase(json, ['substitutions', 'dynamicTemplateData', 'customArgs', 'headers', 'sections']);\n  }\n\n  /**************************************************************************\n   * Static helpers\n   ***/\n\n  /**\n   * Create a Mail instance from given data\n   */\n  static create(data) {\n\n    //Array?\n    if (Array.isArray(data)) {\n      return data\n        .filter(item => !!item)\n        .map(item => this.create(item));\n    }\n\n    //Already instance of Mail class?\n    if (data instanceof Mail) {\n      return data;\n    }\n\n    //Create instance\n    return new Mail(data);\n  }\n\n  /**************************************************************************\n   * helpers for property-setting checks\n   ***/\n\n  /**\n   * Perform a set of checks on the new property value. Returns true if all\n   * checks complete successfully without throwing errors or returning true.\n   */\n  _checkProperty(propertyName, value, checks) {\n    return !checks.some((e) => e(propertyName, value));\n  }\n\n  /**\n   * Set a property with normal undefined and type-checks\n   */\n  _setProperty(propertyName, value, propertyType) {\n    let propertyChecksPassed = this._checkProperty(\n      propertyName,\n      value,\n      [this._checkUndefined, this._createTypeCheck(propertyType)]);\n\n    if (propertyChecksPassed) {\n      this[propertyName] = value;\n    }\n\n    return propertyChecksPassed;\n  }\n\n  /**\n   * Fail if the value is undefined.\n   */\n  _checkUndefined(propertyName, value) {\n    return typeof value === 'undefined';\n  }\n\n  /**\n   * Create and return a function that checks for a given type\n   */\n  _createTypeCheck(propertyType) {\n    return (propertyName, value) => {\n      if (typeof value !== propertyType) {\n        throw new Error(propertyType + ' expected for `' + propertyName + '`');\n      }\n    };\n  }\n\n  /**\n   * Create a check out of a callback. If the callback\n   * returns false, the check will throw an error.\n   */\n  _createCheckThatThrows(check, errorString) {\n    return (propertyName, value) => {\n      if (!check(value)) {\n        throw new Error(errorString);\n      }\n    };\n  }\n\n  /**\n   * Set an array property after checking that the new value is an\n   * array.\n   */\n  _setArrayProperty(propertyName, value) {\n    if (this._doArrayCheck(propertyName, value)) {\n      this[propertyName] = value;\n    }\n  }\n\n  /**\n   * Check that a value isn't undefined and is an array.\n   */\n  _doArrayCheck(propertyName, value) {\n    return this._checkProperty(\n      propertyName,\n      value,\n      [this._checkUndefined, this._createCheckThatThrows(Array.isArray, 'Array expected for`' + propertyName + '`')]);\n  }\n\n  /**\n   * Set the replyToList from email body\n   */\n   setReplyToList(replyToList) {\n    if (this._doArrayCheck('replyToList', replyToList) && replyToList.length) {\n      if (!replyToList.every(replyTo => replyTo && typeof replyTo.email === 'string')) {\n        throw new Error('Expected each replyTo to contain an `email` string');\n      }\n      this.replyToList = replyToList;\n    }\n  }\n}\n\n//Export class\nmodule.exports = Mail;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/classes/mail.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/classes/personalization.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/classes/personalization.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Dependencies\n */\nconst EmailAddress = __webpack_require__(/*! ./email-address */ \"(rsc)/./node_modules/@sendgrid/helpers/classes/email-address.js\");\nconst toCamelCase = __webpack_require__(/*! ../helpers/to-camel-case */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/to-camel-case.js\");\nconst toSnakeCase = __webpack_require__(/*! ../helpers/to-snake-case */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/to-snake-case.js\");\nconst deepClone = __webpack_require__(/*! ../helpers/deep-clone */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/deep-clone.js\");\nconst deepMerge = __webpack_require__(/*! deepmerge */ \"(rsc)/./node_modules/deepmerge/dist/cjs.js\");\nconst wrapSubstitutions = __webpack_require__(/*! ../helpers/wrap-substitutions */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/wrap-substitutions.js\");\n\n/**\n * Personalization class\n */\nclass Personalization {\n\n  /**\n   * Constructor\n   */\n  constructor(data) {\n\n    //Init array and object placeholders\n    this.to = [];\n    this.cc = [];\n    this.bcc = [];\n    this.headers = {};\n    this.customArgs = {};\n    this.substitutions = {};\n    this.substitutionWrappers = ['{{', '}}'];\n    this.dynamicTemplateData = {};\n\n    //Build from data if given\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * From data\n   */\n  fromData(data) {\n\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object for Mail data');\n    }\n\n    //Convert to camel case to make it workable, making a copy to prevent\n    //changes to the original objects\n    data = deepClone(data);\n    data = toCamelCase(data, ['substitutions', 'dynamicTemplateData', 'customArgs', 'headers']);\n\n    //Extract properties from data\n    const {\n      to, from, cc, bcc, subject, headers, customArgs, sendAt,\n      substitutions, substitutionWrappers, dynamicTemplateData,\n    } = data;\n\n    //Set data\n    this.setTo(to);\n    this.setFrom(from);\n    this.setCc(cc);\n    this.setBcc(bcc);\n    this.setSubject(subject);\n    this.setHeaders(headers);\n    this.setSubstitutions(substitutions);\n    this.setSubstitutionWrappers(substitutionWrappers);\n    this.setCustomArgs(customArgs);\n    this.setDynamicTemplateData(dynamicTemplateData);\n    this.setSendAt(sendAt);\n  }\n\n  /**\n   * Set subject\n   */\n  setSubject(subject) {\n    if (typeof subject === 'undefined') {\n      return;\n    }\n    if (typeof subject !== 'string') {\n      throw new Error('String expected for `subject`');\n    }\n    this.subject = subject;\n  }\n\n  /**\n   * Set send at\n   */\n  setSendAt(sendAt) {\n    if (typeof sendAt === 'undefined') {\n      return;\n    }\n    if (!Number.isInteger(sendAt)) {\n      throw new Error('Integer expected for `sendAt`');\n    }\n    this.sendAt = sendAt;\n  }\n\n  /**\n   * Set to\n   */\n  setTo(to) {\n    if (typeof to === 'undefined') {\n      return;\n    }\n    if (!Array.isArray(to)) {\n      to = [to];\n    }\n    this.to = EmailAddress.create(to);\n  }\n\n  /**\n   * Set from\n   * */\n  setFrom(from) {\n    if (typeof from === 'undefined') {\n      return;\n    }\n    this.from = EmailAddress.create(from);\n  }\n\n  /**\n   * Add a single to\n   */\n  addTo(to) {\n    if (typeof to === 'undefined') {\n      return;\n    }\n    this.to.push(EmailAddress.create(to));\n  }\n\n  /**\n   * Set cc\n   */\n  setCc(cc) {\n    if (typeof cc === 'undefined') {\n      return;\n    }\n    if (!Array.isArray(cc)) {\n      cc = [cc];\n    }\n    this.cc = EmailAddress.create(cc);\n  }\n\n  /**\n   * Add a single cc\n   */\n  addCc(cc) {\n    if (typeof cc === 'undefined') {\n      return;\n    }\n    this.cc.push(EmailAddress.create(cc));\n  }\n\n  /**\n   * Set bcc\n   */\n  setBcc(bcc) {\n    if (typeof bcc === 'undefined') {\n      return;\n    }\n    if (!Array.isArray(bcc)) {\n      bcc = [bcc];\n    }\n    this.bcc = EmailAddress.create(bcc);\n  }\n\n  /**\n   * Add a single bcc\n   */\n  addBcc(bcc) {\n    if (typeof bcc === 'undefined') {\n      return;\n    }\n    this.bcc.push(EmailAddress.create(bcc));\n  }\n\n  /**\n   * Set headers\n   */\n  setHeaders(headers) {\n    if (typeof headers === 'undefined') {\n      return;\n    }\n    if (typeof headers !== 'object' || headers === null) {\n      throw new Error('Object expected for `headers`');\n    }\n    this.headers = headers;\n  }\n\n  /**\n   * Add a header\n   */\n  addHeader(key, value) {\n    if (typeof key !== 'string') {\n      throw new Error('String expected for header key');\n    }\n    if (typeof value !== 'string') {\n      throw new Error('String expected for header value');\n    }\n    this.headers[key] = value;\n  }\n\n  /**\n   * Set custom args\n   */\n  setCustomArgs(customArgs) {\n    if (typeof customArgs === 'undefined') {\n      return;\n    }\n    if (typeof customArgs !== 'object' || customArgs === null) {\n      throw new Error('Object expected for `customArgs`');\n    }\n    this.customArgs = customArgs;\n  }\n\n  /**\n   * Add a custom arg\n   */\n  addCustomArg(key, value) {\n    if (typeof key !== 'string') {\n      throw new Error('String expected for custom arg key');\n    }\n    if (typeof value !== 'string') {\n      throw new Error('String expected for custom arg value');\n    }\n    this.customArgs[key] = value;\n  }\n\n  /**\n   * Set substitutions\n   */\n  setSubstitutions(substitutions) {\n    if (typeof substitutions === 'undefined') {\n      return;\n    }\n    if (typeof substitutions !== 'object') {\n      throw new Error('Object expected for `substitutions`');\n    }\n    this.substitutions = substitutions;\n  }\n\n  /**\n   * Add a substitution\n   */\n  addSubstitution(key, value) {\n    if (typeof key !== 'string') {\n      throw new Error('String expected for substitution key');\n    }\n    if (typeof value !== 'string' && typeof value !== 'number') {\n      throw new Error('String or Number expected for substitution value');\n    }\n    this.substitutions[key] = value;\n  }\n\n  /**\n   * Reverse merge substitutions, preserving existing ones\n   */\n  reverseMergeSubstitutions(substitutions) {\n    if (typeof substitutions === 'undefined' || substitutions === null) {\n      return;\n    }\n    if (typeof substitutions !== 'object') {\n      throw new Error(\n        'Object expected for `substitutions` in reverseMergeSubstitutions'\n      );\n    }\n    this.substitutions = Object.assign({}, substitutions, this.substitutions);\n  }\n\n  /**\n   * Set substitution wrappers\n   */\n  setSubstitutionWrappers(wrappers) {\n    if (typeof wrappers === 'undefined' || wrappers === null) {\n      return;\n    }\n\n    if (!Array.isArray(wrappers) || wrappers.length !== 2) {\n      throw new Error(\n        'Array expected with two elements for `substitutionWrappers`'\n      );\n    }\n    this.substitutionWrappers = wrappers;\n  }\n\n  /**\n   * Reverse merge dynamic template data, preserving existing ones\n   */\n  deepMergeDynamicTemplateData(dynamicTemplateData) {\n    if (typeof dynamicTemplateData === 'undefined' || dynamicTemplateData === null) {\n      return;\n    }\n    if (typeof dynamicTemplateData !== 'object') {\n      throw new Error(\n        'Object expected for `dynamicTemplateData` in deepMergeDynamicTemplateData'\n      );\n    }\n    this.dynamicTemplateData = deepMerge(dynamicTemplateData, this.dynamicTemplateData);\n  }\n\n  /**\n   * Set dynamic template data\n   */\n  setDynamicTemplateData(dynamicTemplateData) {\n    if (typeof dynamicTemplateData === 'undefined') {\n      return;\n    }\n    if (typeof dynamicTemplateData !== 'object') {\n      throw new Error('Object expected for `dynamicTemplateData`');\n    }\n    this.dynamicTemplateData = dynamicTemplateData;\n  }\n\n  /**\n   * To JSON\n   */\n  toJSON() {\n\n    //Get data from self\n    const {\n      to, from, cc, bcc, subject, headers, customArgs, sendAt,\n      substitutions, substitutionWrappers, dynamicTemplateData,\n    } = this;\n\n    //Initialize with mandatory values\n    const json = {to};\n\n    //Arrays\n    if (Array.isArray(cc) && cc.length > 0) {\n      json.cc = cc;\n    }\n    if (Array.isArray(bcc) && bcc.length > 0) {\n      json.bcc = bcc;\n    }\n\n    //Objects\n    if (Object.keys(headers).length > 0) {\n      json.headers = headers;\n    }\n    if (substitutions && Object.keys(substitutions).length > 0) {\n      const [left, right] = substitutionWrappers;\n      json.substitutions = wrapSubstitutions(substitutions, left, right);\n    }\n    if (Object.keys(customArgs).length > 0) {\n      json.customArgs = customArgs;\n    }\n\n    if (dynamicTemplateData && Object.keys(dynamicTemplateData).length > 0) {\n      json.dynamicTemplateData = dynamicTemplateData;\n    }\n\n    //Simple properties\n    if (typeof subject !== 'undefined') {\n      json.subject = subject;\n    }\n    if (typeof sendAt !== 'undefined') {\n      json.sendAt = sendAt;\n    }\n    if (typeof from !== 'undefined') {\n      json.from = from;\n    }\n\n    //Return as snake cased object\n    return toSnakeCase(json, ['substitutions', 'dynamicTemplateData', 'customArgs', 'headers']);\n  }\n}\n\n//Export class\nmodule.exports = Personalization;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/classes/personalization.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/classes/response-error.js":
/*!******************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/classes/response-error.js ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * Response error class\n */\nclass ResponseError extends Error {\n\n  /**\n   * Constructor\n   */\n  constructor(response) {\n\n    //Super\n    super();\n\n    //Extract data from response\n    const { headers, status, statusText, data } = response;\n\n    //Set data\n    this.code = status;\n    this.message = statusText;\n    this.response = { headers, body: data };\n\n    //Capture stack trace\n    if (!this.stack) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n\n    //Clean up stack trace\n    const regex = new RegExp(process.cwd() + '/', 'gi');\n    this.stack = this.stack.replace(regex, '');\n  }\n\n  /**\n   * Convert to string\n   */\n  toString() {\n    const { body } = this.response;\n    let err = `${this.message} (${this.code})`;\n    if (body && Array.isArray(body.errors)) {\n      body.errors.forEach(error => {\n        const message = error.message;\n        const field = error.field;\n        const help = error.help;\n        err += `\\n  ${message}\\n    ${field}\\n    ${help}`;\n      });\n    }\n    return err;\n  }\n\n  /**\n   * Convert to simple object for JSON responses\n   */\n  toJSON() {\n    const { message, code, response } = this;\n    return { message, code, response };\n  }\n}\n\n//Export\nmodule.exports = ResponseError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/classes/response-error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/classes/response.js":
/*!************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/classes/response.js ***!
  \************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nclass Response {\n  constructor(statusCode, body, headers) {\n    this.statusCode = statusCode;\n    this.body = body;\n    this.headers = headers;\n  }\n\n  toString() {\n    return 'HTTP ' + this.statusCode + ' ' + this.body;\n  }\n}\n\nmodule.exports = Response;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvY2xhc3Nlcy9yZXNwb25zZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibGFjay1ibG9nLy4vbm9kZV9tb2R1bGVzL0BzZW5kZ3JpZC9oZWxwZXJzL2NsYXNzZXMvcmVzcG9uc2UuanM/MmQ4MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmNsYXNzIFJlc3BvbnNlIHtcbiAgY29uc3RydWN0b3Ioc3RhdHVzQ29kZSwgYm9keSwgaGVhZGVycykge1xuICAgIHRoaXMuc3RhdHVzQ29kZSA9IHN0YXR1c0NvZGU7XG4gICAgdGhpcy5ib2R5ID0gYm9keTtcbiAgICB0aGlzLmhlYWRlcnMgPSBoZWFkZXJzO1xuICB9XG5cbiAgdG9TdHJpbmcoKSB7XG4gICAgcmV0dXJuICdIVFRQICcgKyB0aGlzLnN0YXR1c0NvZGUgKyAnICcgKyB0aGlzLmJvZHk7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBSZXNwb25zZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/classes/response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/classes/statistics.js":
/*!**************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/classes/statistics.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Dependencies\n */\nconst toCamelCase = __webpack_require__(/*! ../helpers/to-camel-case */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/to-camel-case.js\");\nconst deepClone = __webpack_require__(/*! ../helpers/deep-clone */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/deep-clone.js\");\n\n/**\n * Options\n */\nconst AggregatedByOptions = ['day', 'week', 'month'];\nconst CountryOptions = ['us', 'ca'];\nconst SortByDirection = ['desc', 'asc'];\n\n/**\n * Statistics class\n */\nclass Statistics {\n  constructor(data) {\n    this.startDate = null;\n    this.endDate = null;\n    this.aggregatedBy = null;\n\n    if (data) {\n      this.fromData(data);\n    }\n  }\n\n  /**\n   * Build from data\n   */\n  fromData(data) {\n\n    //Expecting object\n    if (typeof data !== 'object') {\n      throw new Error('Expecting object for Statistics data');\n    }\n\n    //Convert to camel case to make it workable, making a copy to prevent\n    //changes to the original objects\n    data = deepClone(data);\n    data = toCamelCase(data, ['substitutions', 'customArgs']);\n\n    const { startDate,\n      endDate,\n      aggregatedBy,\n    } = data;\n\n    this.setStartDate(startDate);\n    this.setEndDate(endDate);\n    this.setAggregatedBy(aggregatedBy);\n  }\n\n  /**\n   * Set startDate\n   */\n  setStartDate(startDate) {\n    if (typeof startDate === 'undefined') {\n      throw new Error('Date expected for `startDate`');\n    }\n\n    if ((new Date(startDate) === 'Invalid Date') ||\n        isNaN(new Date(startDate))) {\n      throw new Error('Date expected for `startDate`');\n    }\n\n    console.log(startDate);\n\n    this.startDate = new Date(startDate).toISOString().slice(0, 10);\n  }\n\n  /**\n   * Set endDate\n   */\n  setEndDate(endDate) {\n    if (typeof endDate === 'undefined') {\n      this.endDate = new Date().toISOString().slice(0, 10);\n      return;\n    }\n\n    if (new Date(endDate) === 'Invalid Date' || isNaN(new Date(endDate))) {\n      throw new Error('Date expected for `endDate`');\n    }\n\n    this.endDate = new Date(endDate).toISOString().slice(0, 10);\n  }\n\n  /**\n   * Set aggregatedBy\n   */\n  setAggregatedBy(aggregatedBy) {\n    if (typeof aggregatedBy === 'undefined') {\n      return;\n    }\n\n    if (typeof aggregatedBy === 'string' &&\n        AggregatedByOptions.includes(aggregatedBy.toLowerCase())) {\n      this.aggregatedBy = aggregatedBy;\n    } else {\n      throw new Error('Incorrect value for `aggregatedBy`');\n    }\n  }\n\n  /**\n   * Get Global\n   */\n  getGlobal() {\n    const { startDate, endDate, aggregatedBy } = this;\n\n    return { startDate, endDate, aggregatedBy };\n  }\n\n  /**\n   * Get Advanced\n   */\n  getAdvanced(country) {\n    const json = this.getGlobal();\n\n    if (typeof country === 'undefined') {\n      return json;\n    }\n\n    if (typeof country === 'string' &&\n        CountryOptions.includes(country.toLowerCase())) {\n      json.country = country;\n    }\n\n    return json;\n  }\n\n  /**\n   * Get Advanced Mailbox Providers\n   */\n  getAdvancedMailboxProviders(mailBoxProviders) {\n    const json = this.getGlobal();\n\n    if (typeof mailBoxProviders === 'undefined') {\n      return json;\n    }\n\n    if (Array.isArray(mailBoxProviders) &&\n        mailBoxProviders.some(x => typeof x !== 'string')) {\n      throw new Error('Array of strings expected for `mailboxProviders`');\n    }\n\n    json.mailBoxProviders = mailBoxProviders;\n\n    return json;\n  }\n\n  /**\n   * Get Advanced Browsers\n   */\n  getAdvancedBrowsers(browsers) {\n    const json = this.getGlobal();\n\n    if (typeof browsers === 'undefined') {\n      return json;\n    }\n\n    if (Array.isArray(browsers) && browsers.some(x => typeof x !== 'string')) {\n      throw new Error('Array of strings expected for `browsers`');\n    }\n\n    json.browsers = browsers;\n\n    return json;\n  }\n\n  /**\n   * Get Categories\n   */\n  getCategories(categories) {\n    if (typeof categories === 'undefined') {\n      throw new Error('Array of strings expected for `categories`');\n    }\n\n    if (!this._isValidArrayOfStrings(categories)) {\n      throw new Error('Array of strings expected for `categories`');\n    }\n\n    const json = this.getGlobal();\n    json.categories = categories;\n\n    return json;\n  }\n\n  /**\n   * Get Subuser\n   */\n  getSubuser(subusers) {\n    if (typeof subusers === 'undefined') {\n      throw new Error('Array of strings expected for `subusers`');\n    }\n\n    if (!this._isValidArrayOfStrings(subusers)) {\n      throw new Error('Array of strings expected for `subusers`');\n    }\n\n    const json = this.getGlobal();\n    json.subusers = subusers;\n\n    return json;\n  }\n\n  /**\n   * Get Subuser Sum\n   */\n  getSubuserSum(sortByMetric = 'delivered',\n    sortByDirection = SortByDirection[0], limit = 5, offset = 0) {\n    if (typeof sortByMetric !== 'string') {\n      throw new Error('string expected for `sortByMetric`');\n    }\n\n    if (!SortByDirection.includes(sortByDirection.toLowerCase())) {\n      throw new Error('desc or asc expected for `sortByDirection`');\n    }\n\n    if (typeof limit !== 'number') {\n      throw new Error('number expected for `limit`');\n    }\n\n    if (typeof offset !== 'number') {\n      throw new Error('number expected for `offset`');\n    }\n\n    const json = this.getGlobal();\n\n    json.sortByMetric = sortByMetric;\n    json.sortByDirection = sortByDirection;\n    json.limit = limit;\n    json.offset = offset;\n\n    return json;\n  }\n\n  /**\n   * Get Subuser Monthly\n   */\n  getSubuserMonthly(sortByMetric = 'delivered',\n    sortByDirection = SortByDirection[0], limit = 5, offset = 0) {\n    if (typeof sortByMetric !== 'string') {\n      throw new Error('string expected for `sortByMetric`');\n    }\n\n    if (!SortByDirection.includes(sortByDirection.toLowerCase())) {\n      throw new Error('desc or asc expected for `sortByDirection`');\n    }\n\n    if (typeof limit !== 'number') {\n      throw new Error('number expected for `limit`');\n    }\n\n    if (typeof offset !== 'number') {\n      throw new Error('number expected for `offset`');\n    }\n\n    const json = this.getGlobal();\n\n    json.sortByMetric = sortByMetric;\n    json.sortByDirection = sortByDirection;\n    json.limit = limit;\n    json.offset = offset;\n\n    return json;\n  }\n\n  _isValidArrayOfStrings(arr) {\n    if (!Array.isArray(arr)) {\n      return false;\n    }\n\n    if (arr.length < 1 || arr.some(x => typeof x !== 'string')) {\n      return false;\n    }\n\n    return true;\n  }\n}\n\n//Export class\nmodule.exports = Statistics;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/classes/statistics.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/constants/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/constants/index.js ***!
  \***********************************************************/
/***/ ((module) => {

eval("const DYNAMIC_TEMPLATE_CHAR_WARNING = `\nContent with characters ', \" or & may need to be escaped with three brackets\n{{{ content }}}\nSee https://sendgrid.com/docs/for-developers/sending-email/using-handlebars/ for more information.`;\n\nmodule.exports = {\n  DYNAMIC_TEMPLATE_CHAR_WARNING,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvY29uc3RhbnRzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmxhY2stYmxvZy8uL25vZGVfbW9kdWxlcy9Ac2VuZGdyaWQvaGVscGVycy9jb25zdGFudHMvaW5kZXguanM/MWNlZSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBEWU5BTUlDX1RFTVBMQVRFX0NIQVJfV0FSTklORyA9IGBcbkNvbnRlbnQgd2l0aCBjaGFyYWN0ZXJzICcsIFwiIG9yICYgbWF5IG5lZWQgdG8gYmUgZXNjYXBlZCB3aXRoIHRocmVlIGJyYWNrZXRzXG57e3sgY29udGVudCB9fX1cblNlZSBodHRwczovL3NlbmRncmlkLmNvbS9kb2NzL2Zvci1kZXZlbG9wZXJzL3NlbmRpbmctZW1haWwvdXNpbmctaGFuZGxlYmFycy8gZm9yIG1vcmUgaW5mb3JtYXRpb24uYDtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIERZTkFNSUNfVEVNUExBVEVfQ0hBUl9XQVJOSU5HLFxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/constants/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/helpers/array-to-json.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/helpers/array-to-json.js ***!
  \*****************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * Helper to convert an array of objects to JSON\n */\nmodule.exports = function arrayToJSON(arr) {\n  return arr.map(item => {\n    if (typeof item === 'object' && item !== null && typeof item.toJSON === 'function') {\n      return item.toJSON();\n    }\n    return item;\n  });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy9hcnJheS10by1qc29uLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy9hcnJheS10by1qc29uLmpzP2M2NGQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG4vKipcbiAqIEhlbHBlciB0byBjb252ZXJ0IGFuIGFycmF5IG9mIG9iamVjdHMgdG8gSlNPTlxuICovXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIGFycmF5VG9KU09OKGFycikge1xuICByZXR1cm4gYXJyLm1hcChpdGVtID0+IHtcbiAgICBpZiAodHlwZW9mIGl0ZW0gPT09ICdvYmplY3QnICYmIGl0ZW0gIT09IG51bGwgJiYgdHlwZW9mIGl0ZW0udG9KU09OID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICByZXR1cm4gaXRlbS50b0pTT04oKTtcbiAgICB9XG4gICAgcmV0dXJuIGl0ZW07XG4gIH0pO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/helpers/array-to-json.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/helpers/convert-keys.js":
/*!****************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/helpers/convert-keys.js ***!
  \****************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * Helper to convert an object's keys\n */\nmodule.exports = function convertKeys(obj, converter, ignored) {\n\n  //Validate\n  if (typeof obj !== 'object' || obj === null) {\n    throw new Error('Non object passed to convertKeys: ' + obj);\n  }\n\n  //Ignore arrays\n  if (Array.isArray(obj)) {\n    return obj;\n  }\n\n  //Ensure array for ignored values\n  if (!Array.isArray(ignored)) {\n    ignored = [];\n  }\n\n  //Process all properties\n  for (const key in obj) {\n    //istanbul ignore else\n    if (obj.hasOwnProperty(key)) {\n\n      //Convert key to snake case\n      const converted = converter(key);\n\n      //Recursive for child objects, unless ignored\n      //The ignored check checks both variants of the key\n      if (typeof obj[key] === 'object' && obj[key] !== null) {\n        if (!ignored.includes(key) && !ignored.includes(converted)) {\n          obj[key] = convertKeys(obj[key], converter, ignored);\n        }\n      }\n\n      //Convert key to snake case and set if needed\n      if (converted !== key) {\n        obj[converted] = obj[key];\n        delete obj[key];\n      }\n    }\n  }\n\n  //Return object\n  return obj;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/helpers/convert-keys.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/helpers/deep-clone.js":
/*!**************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/helpers/deep-clone.js ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * Deep cloning helper for objects\n */\nmodule.exports = function deepClone(obj) {\n  return JSON.parse(JSON.stringify(obj));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy9kZWVwLWNsb25lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy9kZWVwLWNsb25lLmpzPzA1ZjgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG4vKipcbiAqIERlZXAgY2xvbmluZyBoZWxwZXIgZm9yIG9iamVjdHNcbiAqL1xubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBkZWVwQ2xvbmUob2JqKSB7XG4gIHJldHVybiBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KG9iaikpO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/helpers/deep-clone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/helpers/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/helpers/index.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Expose helpers\n */\nconst arrayToJSON = __webpack_require__(/*! ./array-to-json */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/array-to-json.js\");\nconst convertKeys = __webpack_require__(/*! ./convert-keys */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/convert-keys.js\");\nconst deepClone = __webpack_require__(/*! ./deep-clone */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/deep-clone.js\");\nconst mergeData = __webpack_require__(/*! ./merge-data */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/merge-data.js\");\nconst splitNameEmail = __webpack_require__(/*! ./split-name-email */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/split-name-email.js\");\nconst toCamelCase = __webpack_require__(/*! ./to-camel-case */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/to-camel-case.js\");\nconst toSnakeCase = __webpack_require__(/*! ./to-snake-case */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/to-snake-case.js\");\nconst wrapSubstitutions = __webpack_require__(/*! ./wrap-substitutions */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/wrap-substitutions.js\");\n\n/**\n * Export\n */\nmodule.exports = {\n  arrayToJSON,\n  convertKeys,\n  deepClone,\n  mergeData,\n  splitNameEmail,\n  toCamelCase,\n  toSnakeCase,\n  wrapSubstitutions,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsbUJBQU8sQ0FBQyx3RkFBaUI7QUFDN0Msb0JBQW9CLG1CQUFPLENBQUMsc0ZBQWdCO0FBQzVDLGtCQUFrQixtQkFBTyxDQUFDLGtGQUFjO0FBQ3hDLGtCQUFrQixtQkFBTyxDQUFDLGtGQUFjO0FBQ3hDLHVCQUF1QixtQkFBTyxDQUFDLDhGQUFvQjtBQUNuRCxvQkFBb0IsbUJBQU8sQ0FBQyx3RkFBaUI7QUFDN0Msb0JBQW9CLG1CQUFPLENBQUMsd0ZBQWlCO0FBQzdDLDBCQUEwQixtQkFBTyxDQUFDLGtHQUFzQjs7QUFFeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibGFjay1ibG9nLy4vbm9kZV9tb2R1bGVzL0BzZW5kZ3JpZC9oZWxwZXJzL2hlbHBlcnMvaW5kZXguanM/Mjg0MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbi8qKlxuICogRXhwb3NlIGhlbHBlcnNcbiAqL1xuY29uc3QgYXJyYXlUb0pTT04gPSByZXF1aXJlKCcuL2FycmF5LXRvLWpzb24nKTtcbmNvbnN0IGNvbnZlcnRLZXlzID0gcmVxdWlyZSgnLi9jb252ZXJ0LWtleXMnKTtcbmNvbnN0IGRlZXBDbG9uZSA9IHJlcXVpcmUoJy4vZGVlcC1jbG9uZScpO1xuY29uc3QgbWVyZ2VEYXRhID0gcmVxdWlyZSgnLi9tZXJnZS1kYXRhJyk7XG5jb25zdCBzcGxpdE5hbWVFbWFpbCA9IHJlcXVpcmUoJy4vc3BsaXQtbmFtZS1lbWFpbCcpO1xuY29uc3QgdG9DYW1lbENhc2UgPSByZXF1aXJlKCcuL3RvLWNhbWVsLWNhc2UnKTtcbmNvbnN0IHRvU25ha2VDYXNlID0gcmVxdWlyZSgnLi90by1zbmFrZS1jYXNlJyk7XG5jb25zdCB3cmFwU3Vic3RpdHV0aW9ucyA9IHJlcXVpcmUoJy4vd3JhcC1zdWJzdGl0dXRpb25zJyk7XG5cbi8qKlxuICogRXhwb3J0XG4gKi9cbm1vZHVsZS5leHBvcnRzID0ge1xuICBhcnJheVRvSlNPTixcbiAgY29udmVydEtleXMsXG4gIGRlZXBDbG9uZSxcbiAgbWVyZ2VEYXRhLFxuICBzcGxpdE5hbWVFbWFpbCxcbiAgdG9DYW1lbENhc2UsXG4gIHRvU25ha2VDYXNlLFxuICB3cmFwU3Vic3RpdHV0aW9ucyxcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/helpers/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/helpers/merge-data.js":
/*!**************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/helpers/merge-data.js ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * Merge data helper\n */\nmodule.exports = function mergeData(base, data) {\n\n  //Validate data\n  if (typeof base !== 'object' || base === null) {\n    throw new Error('Not an object provided for base');\n  }\n  if (typeof data !== 'object' || data === null) {\n    throw new Error('Not an object provided for data');\n  }\n\n  //Copy base\n  const merged = Object.assign({}, base);\n\n  //Add data\n  for (const key in data) {\n    //istanbul ignore else\n    if (data.hasOwnProperty(key)) {\n      if (data[key] && Array.isArray(data[key])) {\n        merged[key] = data[key];\n      } else if (data[key] && typeof data[key] === 'object') {\n        merged[key] = Object.assign({}, data[key]);\n      } else if (data[key]) {\n        merged[key] = data[key];\n      }\n    }\n  }\n\n  //Return\n  return merged;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy9tZXJnZS1kYXRhLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsaUNBQWlDOztBQUVqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1Isc0NBQXNDO0FBQ3RDLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibGFjay1ibG9nLy4vbm9kZV9tb2R1bGVzL0BzZW5kZ3JpZC9oZWxwZXJzL2hlbHBlcnMvbWVyZ2UtZGF0YS5qcz8yNTQ4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLyoqXG4gKiBNZXJnZSBkYXRhIGhlbHBlclxuICovXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIG1lcmdlRGF0YShiYXNlLCBkYXRhKSB7XG5cbiAgLy9WYWxpZGF0ZSBkYXRhXG4gIGlmICh0eXBlb2YgYmFzZSAhPT0gJ29iamVjdCcgfHwgYmFzZSA9PT0gbnVsbCkge1xuICAgIHRocm93IG5ldyBFcnJvcignTm90IGFuIG9iamVjdCBwcm92aWRlZCBmb3IgYmFzZScpO1xuICB9XG4gIGlmICh0eXBlb2YgZGF0YSAhPT0gJ29iamVjdCcgfHwgZGF0YSA9PT0gbnVsbCkge1xuICAgIHRocm93IG5ldyBFcnJvcignTm90IGFuIG9iamVjdCBwcm92aWRlZCBmb3IgZGF0YScpO1xuICB9XG5cbiAgLy9Db3B5IGJhc2VcbiAgY29uc3QgbWVyZ2VkID0gT2JqZWN0LmFzc2lnbih7fSwgYmFzZSk7XG5cbiAgLy9BZGQgZGF0YVxuICBmb3IgKGNvbnN0IGtleSBpbiBkYXRhKSB7XG4gICAgLy9pc3RhbmJ1bCBpZ25vcmUgZWxzZVxuICAgIGlmIChkYXRhLmhhc093blByb3BlcnR5KGtleSkpIHtcbiAgICAgIGlmIChkYXRhW2tleV0gJiYgQXJyYXkuaXNBcnJheShkYXRhW2tleV0pKSB7XG4gICAgICAgIG1lcmdlZFtrZXldID0gZGF0YVtrZXldO1xuICAgICAgfSBlbHNlIGlmIChkYXRhW2tleV0gJiYgdHlwZW9mIGRhdGFba2V5XSA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgbWVyZ2VkW2tleV0gPSBPYmplY3QuYXNzaWduKHt9LCBkYXRhW2tleV0pO1xuICAgICAgfSBlbHNlIGlmIChkYXRhW2tleV0pIHtcbiAgICAgICAgbWVyZ2VkW2tleV0gPSBkYXRhW2tleV07XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy9SZXR1cm5cbiAgcmV0dXJuIG1lcmdlZDtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/helpers/merge-data.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/helpers/split-name-email.js":
/*!********************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/helpers/split-name-email.js ***!
  \********************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * Split name and email address from string\n */\nmodule.exports = function splitNameEmail(str) {\n\n  //If no email bracket present, return as is\n  if (str.indexOf('<') === -1) {\n    return ['', str];\n  }\n\n  //Split into name and email\n  let [name, email] = str.split('<');\n\n  //Trim and fix up\n  name = name.trim();\n  email = email.replace('>', '').trim();\n\n  //Return as array\n  return [name, email];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy9zcGxpdC1uYW1lLWVtYWlsLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy9zcGxpdC1uYW1lLWVtYWlsLmpzPzAyYjkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG4vKipcbiAqIFNwbGl0IG5hbWUgYW5kIGVtYWlsIGFkZHJlc3MgZnJvbSBzdHJpbmdcbiAqL1xubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBzcGxpdE5hbWVFbWFpbChzdHIpIHtcblxuICAvL0lmIG5vIGVtYWlsIGJyYWNrZXQgcHJlc2VudCwgcmV0dXJuIGFzIGlzXG4gIGlmIChzdHIuaW5kZXhPZignPCcpID09PSAtMSkge1xuICAgIHJldHVybiBbJycsIHN0cl07XG4gIH1cblxuICAvL1NwbGl0IGludG8gbmFtZSBhbmQgZW1haWxcbiAgbGV0IFtuYW1lLCBlbWFpbF0gPSBzdHIuc3BsaXQoJzwnKTtcblxuICAvL1RyaW0gYW5kIGZpeCB1cFxuICBuYW1lID0gbmFtZS50cmltKCk7XG4gIGVtYWlsID0gZW1haWwucmVwbGFjZSgnPicsICcnKS50cmltKCk7XG5cbiAgLy9SZXR1cm4gYXMgYXJyYXlcbiAgcmV0dXJuIFtuYW1lLCBlbWFpbF07XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/helpers/split-name-email.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/helpers/str-to-camel-case.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/helpers/str-to-camel-case.js ***!
  \*********************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * Internal conversion helper\n */\nmodule.exports = function strToCamelCase(str) {\n  if (typeof str !== 'string') {\n    throw new Error('String expected for conversion to snake case');\n  }\n  return str\n    .trim()\n    .replace(/_+|\\-+/g, ' ')\n    .replace(/(?:^\\w|[A-Z]|\\b\\w|\\s+)/g, function(match, index) {\n      if (Number(match) === 0) {\n        return '';\n      }\n      return (index === 0) ? match.toLowerCase() : match.toUpperCase();\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy9zdHItdG8tY2FtZWwtY2FzZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibGFjay1ibG9nLy4vbm9kZV9tb2R1bGVzL0BzZW5kZ3JpZC9oZWxwZXJzL2hlbHBlcnMvc3RyLXRvLWNhbWVsLWNhc2UuanM/ZGYzOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbi8qKlxuICogSW50ZXJuYWwgY29udmVyc2lvbiBoZWxwZXJcbiAqL1xubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBzdHJUb0NhbWVsQ2FzZShzdHIpIHtcbiAgaWYgKHR5cGVvZiBzdHIgIT09ICdzdHJpbmcnKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdTdHJpbmcgZXhwZWN0ZWQgZm9yIGNvbnZlcnNpb24gdG8gc25ha2UgY2FzZScpO1xuICB9XG4gIHJldHVybiBzdHJcbiAgICAudHJpbSgpXG4gICAgLnJlcGxhY2UoL18rfFxcLSsvZywgJyAnKVxuICAgIC5yZXBsYWNlKC8oPzpeXFx3fFtBLVpdfFxcYlxcd3xcXHMrKS9nLCBmdW5jdGlvbihtYXRjaCwgaW5kZXgpIHtcbiAgICAgIGlmIChOdW1iZXIobWF0Y2gpID09PSAwKSB7XG4gICAgICAgIHJldHVybiAnJztcbiAgICAgIH1cbiAgICAgIHJldHVybiAoaW5kZXggPT09IDApID8gbWF0Y2gudG9Mb3dlckNhc2UoKSA6IG1hdGNoLnRvVXBwZXJDYXNlKCk7XG4gICAgfSk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/helpers/str-to-camel-case.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/helpers/str-to-snake-case.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/helpers/str-to-snake-case.js ***!
  \*********************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * Internal conversion helper\n */\nmodule.exports = function strToSnakeCase(str) {\n  if (typeof str !== 'string') {\n    throw new Error('String expected for conversion to snake case');\n  }\n  return str.trim().replace(/(\\s*\\-*\\b\\w|[A-Z])/g, function($1) {\n    $1 = $1.trim().toLowerCase().replace('-', '');\n    return ($1[0] === '_' ? '' : '_') + $1;\n  }).slice(1);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy9zdHItdG8tc25ha2UtY2FzZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy9zdHItdG8tc25ha2UtY2FzZS5qcz9jZjFjIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLyoqXG4gKiBJbnRlcm5hbCBjb252ZXJzaW9uIGhlbHBlclxuICovXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIHN0clRvU25ha2VDYXNlKHN0cikge1xuICBpZiAodHlwZW9mIHN0ciAhPT0gJ3N0cmluZycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ1N0cmluZyBleHBlY3RlZCBmb3IgY29udmVyc2lvbiB0byBzbmFrZSBjYXNlJyk7XG4gIH1cbiAgcmV0dXJuIHN0ci50cmltKCkucmVwbGFjZSgvKFxccypcXC0qXFxiXFx3fFtBLVpdKS9nLCBmdW5jdGlvbigkMSkge1xuICAgICQxID0gJDEudHJpbSgpLnRvTG93ZXJDYXNlKCkucmVwbGFjZSgnLScsICcnKTtcbiAgICByZXR1cm4gKCQxWzBdID09PSAnXycgPyAnJyA6ICdfJykgKyAkMTtcbiAgfSkuc2xpY2UoMSk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/helpers/str-to-snake-case.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/helpers/to-camel-case.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/helpers/to-camel-case.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Dependencies\n */\nconst convertKeys = __webpack_require__(/*! ./convert-keys */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/convert-keys.js\");\nconst strToCamelCase = __webpack_require__(/*! ./str-to-camel-case */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/str-to-camel-case.js\");\n\n/**\n * Convert object keys to camel case\n */\nmodule.exports = function toCamelCase(obj, ignored) {\n  return convertKeys(obj, strToCamelCase, ignored);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy90by1jYW1lbC1jYXNlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixtQkFBTyxDQUFDLHNGQUFnQjtBQUM1Qyx1QkFBdUIsbUJBQU8sQ0FBQyxnR0FBcUI7O0FBRXBEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy90by1jYW1lbC1jYXNlLmpzPzNkOGMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG4vKipcbiAqIERlcGVuZGVuY2llc1xuICovXG5jb25zdCBjb252ZXJ0S2V5cyA9IHJlcXVpcmUoJy4vY29udmVydC1rZXlzJyk7XG5jb25zdCBzdHJUb0NhbWVsQ2FzZSA9IHJlcXVpcmUoJy4vc3RyLXRvLWNhbWVsLWNhc2UnKTtcblxuLyoqXG4gKiBDb252ZXJ0IG9iamVjdCBrZXlzIHRvIGNhbWVsIGNhc2VcbiAqL1xubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiB0b0NhbWVsQ2FzZShvYmosIGlnbm9yZWQpIHtcbiAgcmV0dXJuIGNvbnZlcnRLZXlzKG9iaiwgc3RyVG9DYW1lbENhc2UsIGlnbm9yZWQpO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/helpers/to-camel-case.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/helpers/to-snake-case.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/helpers/to-snake-case.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Dependencies\n */\nconst convertKeys = __webpack_require__(/*! ./convert-keys */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/convert-keys.js\");\nconst strToSnakeCase = __webpack_require__(/*! ./str-to-snake-case */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/str-to-snake-case.js\");\n\n/**\n * Convert object keys to snake case\n */\nmodule.exports = function toSnakeCase(obj, ignored) {\n  return convertKeys(obj, strToSnakeCase, ignored);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy90by1zbmFrZS1jYXNlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixtQkFBTyxDQUFDLHNGQUFnQjtBQUM1Qyx1QkFBdUIsbUJBQU8sQ0FBQyxnR0FBcUI7O0FBRXBEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy90by1zbmFrZS1jYXNlLmpzPzVjMDQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG4vKipcbiAqIERlcGVuZGVuY2llc1xuICovXG5jb25zdCBjb252ZXJ0S2V5cyA9IHJlcXVpcmUoJy4vY29udmVydC1rZXlzJyk7XG5jb25zdCBzdHJUb1NuYWtlQ2FzZSA9IHJlcXVpcmUoJy4vc3RyLXRvLXNuYWtlLWNhc2UnKTtcblxuLyoqXG4gKiBDb252ZXJ0IG9iamVjdCBrZXlzIHRvIHNuYWtlIGNhc2VcbiAqL1xubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiB0b1NuYWtlQ2FzZShvYmosIGlnbm9yZWQpIHtcbiAgcmV0dXJuIGNvbnZlcnRLZXlzKG9iaiwgc3RyVG9TbmFrZUNhc2UsIGlnbm9yZWQpO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/helpers/to-snake-case.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/helpers/validate-settings.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/helpers/validate-settings.js ***!
  \*********************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nconst validate = (parent, parentName, childName, childType) => {\n  if (typeof parent === 'undefined' || typeof parent[childName] === 'undefined') {\n    return;\n  }\n  if (typeof parent[childName] !== childType) {\n    throw new Error(`${childType} expected for \\`${parentName}.${childName}\\``)\n  }\n};\n\nmodule.exports = {\n  validateMailSettings(settings) {\n    if (typeof settings !== 'object') {\n      throw new Error('Object expected for `mailSettings`');\n    }\n    const {\n      bcc,\n      bypassListManagement,\n      bypassSpamManagement,\n      bypassBounceManagement,\n      bypassUnsubscribeManagement,\n      footer,\n      sandboxMode,\n      spamCheck,\n    } = settings;\n    validate(bcc, 'bcc', 'enable', 'boolean');\n    validate(bcc, 'bcc', 'email', 'string');\n    validate(bypassListManagement, 'bypassListManagement', 'enable', 'boolean');\n    validate(bypassSpamManagement, 'bypassSpamManagement', 'enable', 'boolean');\n    validate(bypassBounceManagement, 'bypassBounceManagement', 'enable', 'boolean');\n    validate(bypassUnsubscribeManagement, 'bypassUnsubscribeManagement', 'enable', 'boolean');\n    validate(footer, 'footer', 'enable', 'boolean');\n    validate(footer, 'footer', 'text', 'string');\n    validate(footer, 'footer', 'html', 'string');\n    validate(sandboxMode, 'sandboxMode', 'enable', 'boolean');\n    validate(spamCheck, 'spamCheck', 'enable', 'boolean');\n    validate(spamCheck, 'spamCheck', 'threshold', 'number');\n    validate(spamCheck, 'spamCheck', 'postToUrl', 'string');\n  },\n\n  validateTrackingSettings(settings) {\n    if (typeof settings !== 'object') {\n      throw new Error('Object expected for `trackingSettings`');\n    }\n    const {\n      clickTracking,\n      openTracking,\n      subscriptionTracking,\n      ganalytics,\n    } = settings;\n    validate(clickTracking, 'clickTracking', 'enable', 'boolean');\n    validate(clickTracking, 'clickTracking', 'enableText', 'boolean');\n    validate(openTracking, 'openTracking', 'enable', 'boolean');\n    validate(openTracking, 'openTracking', 'substitutionTag', 'string');\n    validate(subscriptionTracking, 'subscriptionTracking', 'enable', 'boolean');\n    validate(subscriptionTracking, 'subscriptionTracking', 'text', 'string');\n    validate(subscriptionTracking, 'subscriptionTracking', 'html', 'string');\n    validate(subscriptionTracking, 'subscriptionTracking', 'substitutionTag', 'string');\n    validate(ganalytics, 'ganalytics', 'enable', 'boolean');\n    validate(ganalytics, 'ganalytics', 'utm_source', 'string');\n    validate(ganalytics, 'ganalytics', 'utm_medium', 'string');\n    validate(ganalytics, 'ganalytics', 'utm_term', 'string');\n    validate(ganalytics, 'ganalytics', 'utm_content', 'string');\n    validate(ganalytics, 'ganalytics', 'utm_campaign', 'string');\n  },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/helpers/validate-settings.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/helpers/wrap-substitutions.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/helpers/wrap-substitutions.js ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * Wrap substitutions\n */\nmodule.exports = function wrap(substitutions, left = '{{', right = '}}') {\n\n  //Process arrays\n  if (Array.isArray(substitutions)) {\n    return substitutions.map(subs => wrap(subs, left, right));\n  }\n\n  //Initialize new wrapped object\n  const wrapped = {};\n\n  //Map substitutions and ensure string for value\n  for (const key in substitutions) {\n    //istanbul ignore else\n    if (substitutions.hasOwnProperty(key)) {\n      wrapped[left + key + right] = String(substitutions[key]);\n    }\n  }\n\n  //Return wrapped substitutions\n  return wrapped;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaGVscGVycy93cmFwLXN1YnN0aXR1dGlvbnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELGNBQWM7O0FBRXRFO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmxhY2stYmxvZy8uL25vZGVfbW9kdWxlcy9Ac2VuZGdyaWQvaGVscGVycy9oZWxwZXJzL3dyYXAtc3Vic3RpdHV0aW9ucy5qcz8wZTg0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLyoqXG4gKiBXcmFwIHN1YnN0aXR1dGlvbnNcbiAqL1xubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiB3cmFwKHN1YnN0aXR1dGlvbnMsIGxlZnQgPSAne3snLCByaWdodCA9ICd9fScpIHtcblxuICAvL1Byb2Nlc3MgYXJyYXlzXG4gIGlmIChBcnJheS5pc0FycmF5KHN1YnN0aXR1dGlvbnMpKSB7XG4gICAgcmV0dXJuIHN1YnN0aXR1dGlvbnMubWFwKHN1YnMgPT4gd3JhcChzdWJzLCBsZWZ0LCByaWdodCkpO1xuICB9XG5cbiAgLy9Jbml0aWFsaXplIG5ldyB3cmFwcGVkIG9iamVjdFxuICBjb25zdCB3cmFwcGVkID0ge307XG5cbiAgLy9NYXAgc3Vic3RpdHV0aW9ucyBhbmQgZW5zdXJlIHN0cmluZyBmb3IgdmFsdWVcbiAgZm9yIChjb25zdCBrZXkgaW4gc3Vic3RpdHV0aW9ucykge1xuICAgIC8vaXN0YW5idWwgaWdub3JlIGVsc2VcbiAgICBpZiAoc3Vic3RpdHV0aW9ucy5oYXNPd25Qcm9wZXJ0eShrZXkpKSB7XG4gICAgICB3cmFwcGVkW2xlZnQgKyBrZXkgKyByaWdodF0gPSBTdHJpbmcoc3Vic3RpdHV0aW9uc1trZXldKTtcbiAgICB9XG4gIH1cblxuICAvL1JldHVybiB3cmFwcGVkIHN1YnN0aXR1dGlvbnNcbiAgcmV0dXJuIHdyYXBwZWQ7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/helpers/wrap-substitutions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/helpers/index.js":
/*!*************************************************!*\
  !*** ./node_modules/@sendgrid/helpers/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Load support assets\n */\nconst classes = __webpack_require__(/*! ./classes */ \"(rsc)/./node_modules/@sendgrid/helpers/classes/index.js\");\nconst helpers = __webpack_require__(/*! ./helpers */ \"(rsc)/./node_modules/@sendgrid/helpers/helpers/index.js\");\n\n/**\n * Export\n */\nmodule.exports = {classes, helpers};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLG1CQUFPLENBQUMsMEVBQVc7QUFDbkMsZ0JBQWdCLG1CQUFPLENBQUMsMEVBQVc7O0FBRW5DO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL2hlbHBlcnMvaW5kZXguanM/N2VlNiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbi8qKlxuICogTG9hZCBzdXBwb3J0IGFzc2V0c1xuICovXG5jb25zdCBjbGFzc2VzID0gcmVxdWlyZSgnLi9jbGFzc2VzJyk7XG5jb25zdCBoZWxwZXJzID0gcmVxdWlyZSgnLi9oZWxwZXJzJyk7XG5cbi8qKlxuICogRXhwb3J0XG4gKi9cbm1vZHVsZS5leHBvcnRzID0ge2NsYXNzZXMsIGhlbHBlcnN9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/helpers/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/mail/index.js":
/*!**********************************************!*\
  !*** ./node_modules/@sendgrid/mail/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst mailer = __webpack_require__(/*! ./src/mail */ \"(rsc)/./node_modules/@sendgrid/mail/src/mail.js\");\nconst MailService = __webpack_require__(/*! ./src/classes/mail-service */ \"(rsc)/./node_modules/@sendgrid/mail/src/classes/mail-service.js\");\n\nmodule.exports = mailer;\nmodule.exports.MailService = MailService;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL21haWwvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsZUFBZSxtQkFBTyxDQUFDLG1FQUFZO0FBQ25DLG9CQUFvQixtQkFBTyxDQUFDLG1HQUE0Qjs7QUFFeEQ7QUFDQSwwQkFBMEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibGFjay1ibG9nLy4vbm9kZV9tb2R1bGVzL0BzZW5kZ3JpZC9tYWlsL2luZGV4LmpzPzFkMjAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCBtYWlsZXIgPSByZXF1aXJlKCcuL3NyYy9tYWlsJyk7XG5jb25zdCBNYWlsU2VydmljZSA9IHJlcXVpcmUoJy4vc3JjL2NsYXNzZXMvbWFpbC1zZXJ2aWNlJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gbWFpbGVyO1xubW9kdWxlLmV4cG9ydHMuTWFpbFNlcnZpY2UgPSBNYWlsU2VydmljZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/mail/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/mail/src/classes/mail-service.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@sendgrid/mail/src/classes/mail-service.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Dependencies\n */\nconst {Client} = __webpack_require__(/*! @sendgrid/client */ \"(rsc)/./node_modules/@sendgrid/client/index.js\");\nconst {classes: {Mail}} = __webpack_require__(/*! @sendgrid/helpers */ \"(rsc)/./node_modules/@sendgrid/helpers/index.js\");\n\n/**\n * Mail service class\n */\nclass MailService {\n\n  /**\n   * Constructor\n   */\n  constructor() {\n\n    // Set client, initialize substitution wrappers and secret rules filter.\n    this.setClient(new Client());\n    this.setSubstitutionWrappers('{{', '}}');\n    this.secretRules = [];\n  }\n\n  /**\n   * Set client\n   */\n  setClient(client) {\n    this.client = client;\n\n    return this;\n  }\n\n  /**\n   * SendGrid API key passthrough for convenience.\n   */\n  setApiKey(apiKey) {\n    this.client.setApiKey(apiKey);\n\n    return this;\n  }\n\n  /**\n   * Twilio Email Auth passthrough for convenience.\n   */\n  setTwilioEmailAuth(username, password) {\n    this.client.setTwilioEmailAuth(username, password);\n  }\n\n  /**\n   * Set client timeout\n   */\n  setTimeout(timeout) {\n    if (typeof timeout === 'undefined') {\n      return;\n    }\n\n    this.client.setDefaultRequest('timeout', timeout);\n  }\n\n  /**\n   * Set substitution wrappers\n   */\n  setSubstitutionWrappers(left, right) {\n    if (typeof left === 'undefined' || typeof right === 'undefined') {\n      throw new Error('Must provide both left and right side wrappers');\n    }\n    if (!Array.isArray(this.substitutionWrappers)) {\n      this.substitutionWrappers = [];\n    }\n    this.substitutionWrappers[0] = left;\n    this.substitutionWrappers[1] = right;\n\n    return this;\n  }\n\n  /**\n   * Set secret rules for filtering the e-mail content\n   */\n  setSecretRules(rules) {\n    if (!(rules instanceof Array)) {\n      rules = [rules];\n    }\n\n    const tmpRules = rules.map(function (rule) {\n      const ruleType = typeof rule;\n\n      if (ruleType === 'string') {\n        return {\n          pattern: new RegExp(rule),\n        };\n      } else if (ruleType === 'object') {\n        // normalize rule object\n        if (rule instanceof RegExp) {\n          rule = {\n            pattern: rule,\n          };\n        } else if (rule.hasOwnProperty('pattern')\n          && (typeof rule.pattern === 'string')\n        ) {\n          rule.pattern = new RegExp(rule.pattern);\n        }\n\n        try {\n          // test if rule.pattern is a valid regex\n          rule.pattern.test('');\n          return rule;\n        } catch (err) {\n          // continue regardless of error\n        }\n      }\n    });\n\n    this.secretRules = tmpRules.filter(function (val) {\n      return val;\n    });\n  }\n\n  /**\n   * Check if the e-mail is safe to be sent\n   */\n  filterSecrets(body) {\n    if ((typeof body === 'object') && !body.hasOwnProperty('content')) {\n      return;\n    }\n\n    const self = this;\n\n    body.content.forEach(function (data) {\n      self.secretRules.forEach(function (rule) {\n        if (rule.hasOwnProperty('pattern')\n          && !rule.pattern.test(data.value)\n        ) {\n          return;\n        }\n\n        let message = `The pattern '${rule.pattern}'`;\n\n        if (rule.name) {\n          message += `identified by '${rule.name}'`;\n        }\n\n        message += ' was found in the Mail content!';\n\n        throw new Error(message);\n      });\n    });\n  }\n\n  /**\n   * Send email\n   */\n  send(data, isMultiple = false, cb) {\n\n    //Callback as second parameter\n    if (typeof isMultiple === 'function') {\n      cb = isMultiple;\n      isMultiple = false;\n    }\n\n    //Array? Send in parallel\n    if (Array.isArray(data)) {\n\n      //Create promise\n      const promise = Promise.all(data.map(item => {\n        return this.send(item, isMultiple);\n      }));\n\n      //Execute callback if provided\n      if (cb) {\n        promise\n          .then(result => cb(null, result))\n          .catch(error => cb(error, null));\n      }\n\n      //Return promise\n      return promise;\n    }\n\n    //Send mail\n    try {\n\n      //Append multiple flag to data if not set\n      if (typeof data.isMultiple === 'undefined') {\n        data.isMultiple = isMultiple;\n      }\n\n      //Append global substitution wrappers if not set in data\n      if (typeof data.substitutionWrappers === 'undefined') {\n        data.substitutionWrappers = this.substitutionWrappers;\n      }\n\n      //Create Mail instance from data and get JSON body for request\n      const mail = Mail.create(data);\n      const body = mail.toJSON();\n\n      //Filters the Mail body to avoid sensitive content leakage\n      this.filterSecrets(body);\n\n      //Create request\n      const request = {\n        method: 'POST',\n        url: '/v3/mail/send',\n        headers: mail.headers,\n        body,\n      };\n\n      //Send\n      return this.client.request(request, cb);\n    } catch (error) {\n\n      //Pass to callback if provided\n      if (cb) {\n        // eslint-disable-next-line callback-return\n        cb(error, null);\n      }\n\n      //Reject promise\n      return Promise.reject(error);\n    }\n  }\n\n  /**\n   * Send multiple emails (shortcut)\n   */\n  sendMultiple(data, cb) {\n    return this.send(data, true, cb);\n  }\n}\n\n//Export class\nmodule.exports = MailService;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/mail/src/classes/mail-service.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/mail/src/mail.js":
/*!*************************************************!*\
  !*** ./node_modules/@sendgrid/mail/src/mail.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/**\n * Dependencies\n */\nconst MailService = __webpack_require__(/*! ./classes/mail-service */ \"(rsc)/./node_modules/@sendgrid/mail/src/classes/mail-service.js\");\n\n//Export singleton instance\nmodule.exports = new MailService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNlbmRncmlkL21haWwvc3JjL21haWwuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLG1CQUFPLENBQUMsK0ZBQXdCOztBQUVwRDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmxhY2stYmxvZy8uL25vZGVfbW9kdWxlcy9Ac2VuZGdyaWQvbWFpbC9zcmMvbWFpbC5qcz8wOGNkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLyoqXG4gKiBEZXBlbmRlbmNpZXNcbiAqL1xuY29uc3QgTWFpbFNlcnZpY2UgPSByZXF1aXJlKCcuL2NsYXNzZXMvbWFpbC1zZXJ2aWNlJyk7XG5cbi8vRXhwb3J0IHNpbmdsZXRvbiBpbnN0YW5jZVxubW9kdWxlLmV4cG9ydHMgPSBuZXcgTWFpbFNlcnZpY2UoKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sendgrid/mail/src/mail.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@sendgrid/client/package.json":
/*!****************************************************!*\
  !*** ./node_modules/@sendgrid/client/package.json ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"@sendgrid/client","description":"Twilio SendGrid NodeJS API client","version":"8.1.5","author":"Twilio SendGrid <<EMAIL>> (sendgrid.com)","contributors":["Kyle Partridge <<EMAIL>>","David Tomberlin <<EMAIL>>","Swift <<EMAIL>>","Brandon West <<EMAIL>>","Scott Motte <<EMAIL>>","Robert Acosta <<EMAIL>>","Elmer Thomas <<EMAIL>>","Adam Reis <<EMAIL>>"],"license":"MIT","homepage":"https://sendgrid.com","repository":{"type":"git","url":"git://github.com/sendgrid/sendgrid-nodejs.git"},"publishConfig":{"access":"public"},"main":"index.js","engines":{"node":">=12.*"},"dependencies":{"@sendgrid/helpers":"^8.0.0","axios":"^1.8.2"},"devDependencies":{"chai":"4.2.0","nock":"^10.0.6"},"resolutions":{"chai":"4.2.0"},"tags":["http","rest","api","mail","sendgrid"],"gitHead":"2bac86884f71be3fb19f96a10c02a1fb616b81fc"}');

/***/ })

};
;