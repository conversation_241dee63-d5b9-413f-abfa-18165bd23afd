// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// NextAuth.js Models
model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// String constants for SQLite compatibility
// UserRole: READER, AUTHOR, ADMIN
// SubscriptionTier: FREE, PREMIUM, PREMIUM_PLUS
// SubscriptionStatus: ACTIVE, CANCELED, PAST_DUE, UNPAID, INCOMPLETE, INCOMPLETE_EXPIRED, TRIALING
// PaymentStatus: PENDING, COMPLETED, FAILED, CANCELED, REFUNDED
// PayoutStatus: PENDING, PROCESSING, COMPLETED, FAILED
// ContentType: NOVEL, CHAPTER
// EarningType: SUBSCRIPTION_REVENUE, TIP, BONUS, REFERRAL, CREDIT_PURCHASE
// CreditTransactionType: PURCHASE, SPEND, REFUND, BONUS, ADMIN_ADJUSTMENT
// CreditTransactionStatus: PENDING, COMPLETED, FAILED, CANCELED
// NovelStatus: DRAFT, PUBLISHED, COMPLETED, ARCHIVED
// ChapterStatus: DRAFT, PUBLISHED

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime? @map("email_verified")
  image         String?
  role          String    @default("READER")
  bio           String?
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  // Monetization fields
  stripeCustomerId String? @unique @map("stripe_customer_id")
  stripeAccountId  String? @unique @map("stripe_account_id") // For Connect accounts (writers)

  // Credit system fields
  creditBalance    Int     @default(0) @map("credit_balance") // User's current credit balance

  // Notification preferences
  emailNotifications      Boolean   @default(true) @map("email_notifications")
  lastLowBalanceWarning   DateTime? @map("last_low_balance_warning")

  // Relations
  accounts        Account[]         @relation
  sessions        Session[]         @relation
  novels          Novel[]           @relation("AuthorNovels")
  library         Library[]         @relation
  readingProgress ReadingProgress[] @relation

  // Monetization relations
  subscriptions   Subscription[]    @relation
  payments        Payment[]         @relation
  earnings        Earning[]         @relation
  payouts         Payout[]          @relation
  tips            Tip[]             @relation("TipSender")
  receivedTips    Tip[]             @relation("TipReceiver")

  // Credit system relations
  creditTransactions CreditTransaction[] @relation
  creditPurchases    CreditPurchase[]    @relation
  contentPurchases   ContentPurchase[]   @relation

  @@map("users")
}

model Novel {
  id          String      @id @default(cuid())
  title       String
  description String?
  synopsis    String?
  coverImage  String?     @map("cover_image")
  status      String      @default("DRAFT")
  genre       String?
  tags        String?     // JSON string for SQLite compatibility
  authorId    String      @map("author_id")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")
  publishedAt DateTime?   @map("published_at")

  // Monetization fields
  isPremium           Boolean           @default(false) @map("is_premium")
  requiredTier        String?           @map("required_tier")
  price               Float?            // For individual purchase
  creditPrice         Int?              @map("credit_price") // Price in credits
  revenueSharePercent Int               @default(70) @map("revenue_share_percent") // Platform takes 30%, author gets 70%

  // Relations
  author          User              @relation("AuthorNovels", fields: [authorId], references: [id], onDelete: Cascade)
  chapters        Chapter[]         @relation
  library         Library[]         @relation
  readingProgress ReadingProgress[] @relation

  @@map("novels")
}

model Chapter {
  id        String        @id @default(cuid())
  title     String
  content   String
  order     Int
  status    String        @default("DRAFT")
  novelId   String        @map("novel_id")
  createdAt DateTime      @default(now()) @map("created_at")
  updatedAt DateTime      @updatedAt @map("updated_at")

  // Monetization fields
  isPremium    Boolean           @default(false) @map("is_premium")
  requiredTier String?           @map("required_tier")
  price        Float?            // For individual chapter purchase
  creditPrice  Int?              @map("credit_price") // Price in credits

  // Relations
  novel                Novel             @relation(fields: [novelId], references: [id], onDelete: Cascade)
  readingProgress      ReadingProgress[] @relation
  lastChapterProgress  ReadingProgress[] @relation("LastChapterRead")

  @@unique([novelId, order])
  @@map("chapters")
}

model Library {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  novelId   String   @map("novel_id")
  addedAt   DateTime @default(now()) @map("added_at")

  // Relations
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  novel Novel @relation(fields: [novelId], references: [id], onDelete: Cascade)

  @@unique([userId, novelId])
  @@map("library")
}

model ReadingProgress {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  novelId         String   @map("novel_id")
  chapterId       String?  @map("chapter_id")
  lastChapterId   String?  @map("last_chapter_id")
  progress        Float    @default(0) // Percentage (0-100)
  lastReadAt      DateTime @default(now()) @map("last_read_at")
  totalTimeRead   Int      @default(0) @map("total_time_read") // in seconds
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  novel       Novel    @relation(fields: [novelId], references: [id], onDelete: Cascade)
  chapter     Chapter? @relation(fields: [chapterId], references: [id], onDelete: SetNull)
  lastChapter Chapter? @relation("LastChapterRead", fields: [lastChapterId], references: [id], onDelete: SetNull)

  @@unique([userId, novelId])
  @@map("reading_progress")
}

// Monetization Models

model SubscriptionTierConfig {
  id          String           @id @default(cuid())
  tier        String           @unique
  name        String
  description String?
  price       Float            // Monthly price
  yearlyPrice Float?           // Yearly price (optional discount)
  features    String?          // JSON string for SQLite compatibility
  isActive    Boolean          @default(true) @map("is_active")
  createdAt   DateTime         @default(now()) @map("created_at")
  updatedAt   DateTime         @updatedAt @map("updated_at")

  // Relations
  subscriptions Subscription[] @relation

  @@map("subscription_tier_configs")
}

model Subscription {
  id                   String             @id @default(cuid())
  userId               String             @map("user_id")
  tier                 String
  status               String             @default("ACTIVE")
  stripeSubscriptionId String?            @unique @map("stripe_subscription_id")
  stripePriceId        String?            @map("stripe_price_id")
  currentPeriodStart   DateTime           @map("current_period_start")
  currentPeriodEnd     DateTime           @map("current_period_end")
  cancelAtPeriodEnd    Boolean            @default(false) @map("cancel_at_period_end")
  canceledAt           DateTime?          @map("canceled_at")
  trialStart           DateTime?          @map("trial_start")
  trialEnd             DateTime?          @map("trial_end")
  createdAt            DateTime           @default(now()) @map("created_at")
  updatedAt            DateTime           @updatedAt @map("updated_at")

  // Relations
  user       User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  tierConfig SubscriptionTierConfig @relation(fields: [tier], references: [tier])
  payments   Payment[]              @relation

  @@map("subscriptions")
}

model Payment {
  id                 String        @id @default(cuid())
  userId             String        @map("user_id")
  subscriptionId     String?       @map("subscription_id")
  stripePaymentId    String?       @unique @map("stripe_payment_id")
  amount             Float
  currency           String        @default("usd")
  status             String        @default("PENDING")
  description        String?
  metadata           String?       // JSON string for SQLite compatibility
  failureReason      String?       @map("failure_reason")
  refundedAmount     Float?        @map("refunded_amount")
  createdAt          DateTime      @default(now()) @map("created_at")
  updatedAt          DateTime      @updatedAt @map("updated_at")

  // Relations
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscription Subscription? @relation(fields: [subscriptionId], references: [id], onDelete: SetNull)

  @@map("payments")
}

model Earning {
  id          String      @id @default(cuid())
  userId      String      @map("user_id") // Author who earned
  type        String
  amount      Float
  currency    String      @default("usd")
  description String?

  // Source tracking
  sourceType         String?      @map("source_type") // NOVEL or CHAPTER
  sourceId           String?      @map("source_id") // Novel or Chapter ID
  subscriptionId     String?      @map("subscription_id") // If from subscription
  tipId              String?      @map("tip_id") // If from tip
  contentPurchaseId  String?      @map("content_purchase_id") // If from credit purchase

  // Revenue sharing
  platformFee      Float        @map("platform_fee")
  authorEarning    Float        @map("author_earning")

  // Payout tracking
  payoutId         String?      @map("payout_id")
  isPaidOut        Boolean      @default(false) @map("is_paid_out")

  createdAt        DateTime     @default(now()) @map("created_at")
  updatedAt        DateTime     @updatedAt @map("updated_at")

  // Relations
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  payout Payout? @relation(fields: [payoutId], references: [id], onDelete: SetNull)

  @@map("earnings")
}

model Payout {
  id                String       @id @default(cuid())
  userId            String       @map("user_id") // Author receiving payout
  stripeTransferId  String?      @unique @map("stripe_transfer_id")
  amount            Float
  currency          String       @default("usd")
  status            String       @default("PENDING")
  description       String?
  failureReason     String?      @map("failure_reason")

  // Period tracking
  periodStart       DateTime     @map("period_start")
  periodEnd         DateTime     @map("period_end")

  // Processing dates
  scheduledAt       DateTime?    @map("scheduled_at")
  processedAt       DateTime?    @map("processed_at")

  createdAt         DateTime     @default(now()) @map("created_at")
  updatedAt         DateTime     @updatedAt @map("updated_at")

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  earnings Earning[] @relation

  @@map("payouts")
}

model Tip {
  id        String   @id @default(cuid())
  senderId  String   @map("sender_id")
  receiverId String  @map("receiver_id")
  amount    Float
  currency  String   @default("usd")
  message   String?

  // Source content (what was tipped for)
  sourceType String?      @map("source_type") // NOVEL or CHAPTER
  sourceId   String?      @map("source_id") // Novel or Chapter ID

  // Payment tracking
  stripePaymentId String? @unique @map("stripe_payment_id")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  sender   User @relation("TipSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiver User @relation("TipReceiver", fields: [receiverId], references: [id], onDelete: Cascade)

  @@map("tips")
}

// Credit System Models
model CreditPackage {
  id          String  @id @default(cuid())
  name        String  // e.g., "Starter Pack", "Value Pack", "Premium Pack"
  description String?
  credits     Int     // Number of credits in this package
  price       Float   // Price in USD
  currency    String  @default("usd")

  // Bonus credits for bulk purchases
  bonusCredits Int @default(0) @map("bonus_credits")

  // Package configuration
  isActive    Boolean @default(true) @map("is_active")
  sortOrder   Int     @default(0) @map("sort_order")

  // Stripe integration
  stripePriceId String? @unique @map("stripe_price_id")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  purchases CreditPurchase[] @relation

  @@map("credit_packages")
}

model CreditPurchase {
  id        String        @id @default(cuid())
  userId    String        @map("user_id")
  packageId String        @map("package_id")

  // Purchase details
  credits      Int     // Credits purchased
  bonusCredits Int     @default(0) @map("bonus_credits")
  totalCredits Int     @map("total_credits") // credits + bonusCredits
  amount       Float
  currency     String  @default("usd")

  // Payment tracking
  stripePaymentId String?       @unique @map("stripe_payment_id")
  status          String        @default("PENDING")
  failureReason   String?       @map("failure_reason")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user    User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  package CreditPackage @relation(fields: [packageId], references: [id], onDelete: Restrict)

  @@map("credit_purchases")
}

model CreditTransaction {
  id     String                 @id @default(cuid())
  userId String                 @map("user_id")
  type   String
  status String                  @default("PENDING")

  // Transaction details
  amount      Int     // Credits amount (positive for credit, negative for debit)
  description String?

  // Source tracking
  sourceType String? @map("source_type") // "purchase", "content_unlock", "refund", etc.
  sourceId   String? @map("source_id")   // ID of related record

  // Balance tracking
  balanceBefore Int @map("balance_before")
  balanceAfter  Int @map("balance_after")

  // Related records
  purchaseId        String? @map("purchase_id")        // If from credit purchase
  contentPurchaseId String? @map("content_purchase_id") // If from content purchase

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  contentPurchase ContentPurchase? @relation(fields: [contentPurchaseId], references: [id], onDelete: SetNull)

  @@map("credit_transactions")
}

model ContentPurchase {
  id     String      @id @default(cuid())
  userId String      @map("user_id")

  // Content details
  contentType String      // NOVEL or CHAPTER
  contentId   String      @map("content_id") // Novel or Chapter ID

  // Purchase details
  creditsSpent Int     @map("credits_spent")
  priceAtTime  Float   @map("price_at_time") // Original price in USD

  // Access details
  accessGranted Boolean   @default(true) @map("access_granted")
  expiresAt     DateTime? @map("expires_at") // For time-limited access

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user         User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions CreditTransaction[] @relation

  @@unique([userId, contentId])
  @@map("content_purchases")
}