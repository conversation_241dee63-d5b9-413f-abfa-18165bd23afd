"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useDispatch, useSelector } from "react-redux"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CreditPurchaseModal } from "@/components/credits/credit-purchase-modal"
import { openPurchaseModal, selectPurchaseModal } from "@/store/slices/creditSlice"
import type { AppDispatch } from "@/lib/store"

export default function TestCreditsPage() {
  const { data: session } = useSession()
  const dispatch = useDispatch<AppDispatch>()
  const { isOpen } = useSelector(selectPurchaseModal)

  const handleOpenModal = () => {
    console.log('Test button clicked!')
    console.log('Session:', session)
    console.log('Current modal state:', isOpen)
    dispatch(openPurchaseModal(null))
    console.log('Dispatched openPurchaseModal')
  }

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Credit System Test Page</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold">Session Info:</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm">
              {JSON.stringify(session, null, 2)}
            </pre>
          </div>
          
          <div>
            <h3 className="font-semibold">Modal State:</h3>
            <p>Is Open: {isOpen ? 'true' : 'false'}</p>
          </div>

          <Button onClick={handleOpenModal} size="lg">
            Test Open Credit Purchase Modal
          </Button>

          <div className="text-sm text-gray-600">
            <p>This button should:</p>
            <ul className="list-disc list-inside">
              <li>Log messages to console</li>
              <li>Open the credit purchase modal</li>
              <li>Show your session information</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      <CreditPurchaseModal />
    </div>
  )
}
