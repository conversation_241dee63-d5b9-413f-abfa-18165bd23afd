import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { formatCredits, creditsToUSD } from "@/lib/credits"

// GET /api/credits/balance/enhanced - Get enhanced credit balance with spending insights
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const includeRecommendations = searchParams.get("recommendations") === "true"

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        creditBalance: true,
        lastLowBalanceWarning: true,
      }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Get recent spending patterns
    const recentTransactions = await prisma.creditTransaction.findMany({
      where: {
        userId: session.user.id,
        type: 'SPEND',
        status: 'COMPLETED',
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
      select: {
        amount: true,
        createdAt: true,
        sourceType: true,
      }
    })

    // Calculate spending insights
    const totalSpent = recentTransactions.reduce((sum, tx) => sum + Math.abs(tx.amount, 0)
    const averageSpending = recentTransactions.length > 0 ? totalSpent / recentTransactions.length : 0
    const spendingFrequency = recentTransactions.length

    // Get content purchase history
    const contentPurchases = await prisma.contentPurchase.findMany({
      where: { userId: session.user.id },
      select: {
        contentType: true,
        creditPrice: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'desc' },
      take: 5,
    })

    // Calculate spending power (how many average chapters user can afford)
    const averageChapterPrice = 5 // Default chapter price
    const spendingPower = Math.floor(user.creditBalance / averageChapterPrice)

    // Determine balance status
    let balanceStatus: 'healthy' | 'low' | 'critical' = 'healthy'
    if (user.creditBalance < 10) {
      balanceStatus = 'critical'
    } else if (user.creditBalance < 25) {
      balanceStatus = 'low'
    }

    const response: any = {
      balance: user.creditBalance,
      balanceFormatted: formatCredits(user.creditBalance),
      balanceUSD: creditsToUSD(user.creditBalance),
      userId: user.id,
      status: balanceStatus,
      spendingPower: {
        estimatedChapters: spendingPower,
        description: `Can unlock approximately ${spendingPower} chapters`
      },
      insights: {
        totalSpentLast30Days: totalSpent,
        averageTransactionAmount: Math.round(averageSpending),
        transactionFrequency: spendingFrequency,
        lastPurchases: contentPurchases.map(purchase => ({
          type: purchase.contentType,
          credits: purchase.creditPrice,
          date: purchase.createdAt,
        }))
      }
    }

    // Add recommendations if requested
    if (includeRecommendations) {
      const recommendations = []

      if (balanceStatus === 'low' || balanceStatus === 'critical') {
        recommendations.push({
          type: 'low_balance',
          title: 'Low Credit Balance',
          description: 'Consider purchasing more credits to continue enjoying premium content',
          action: 'purchase_credits',
          priority: balanceStatus === 'critical' ? 'high' : 'medium'
        })
      }

      if (spendingFrequency > 0 && averageSpending > 0) {
        const suggestedPackageSize = Math.ceil(averageSpending * 10) // 10 transactions worth
        recommendations.push({
          type: 'bulk_purchase',
          title: 'Bulk Purchase Suggestion',
          description: `Based on your spending pattern, consider buying ${formatCredits(suggestedPackageSize)} for better value`,
          action: 'view_packages',
          priority: 'low'
        })
      }

      response.recommendations = recommendations
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error("Error fetching enhanced credit balance:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/credits/balance/enhanced - Check if user can afford specific content
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { contentIds, contentType } = body

    if (!Array.isArray(contentIds) || !contentType) {
      return NextResponse.json({ error: "Invalid request body" }, { status: 400 })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { creditBalance: true }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Get content prices
    let contents: any[] = []
    if (contentType === 'CHAPTER') {
      contents = await prisma.chapter.findMany({
        where: { id: { in: contentIds } },
        select: {
          id: true,
          title: true,
          creditPrice: true,
          isPremium: true,
        }
      })
    }

    // Calculate affordability
    const affordabilityCheck = contents.map(content => {
      const canAfford = !content.isPremium || 
                       !content.creditPrice || 
                       user.creditBalance >= content.creditPrice

      return {
        contentId: content.id,
        title: content.title,
        creditPrice: content.creditPrice,
        canAfford,
        shortfall: canAfford ? 0 : (content.creditPrice || 0) - user.creditBalance
      }
    })

    const totalCost = contents.reduce((sum, content) => 
      sum + (content.isPremium ? (content.creditPrice || 0) : 0), 0
    )

    return NextResponse.json({
      userBalance: user.creditBalance,
      totalCost,
      canAffordAll: user.creditBalance >= totalCost,
      shortfall: Math.max(0, totalCost - user.creditBalance),
      contents: affordabilityCheck
    })

  } catch (error) {
    console.error("Error checking content affordability:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
