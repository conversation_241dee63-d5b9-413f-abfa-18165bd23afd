import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { PaymentStatus } from "@prisma/client"

// GET /api/credits/purchases/[id]/receipt - Generate purchase receipt
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get the purchase
    const purchase = await prisma.creditPurchase.findUnique({
      where: { 
        id: params.id,
        userId: session.user.id // Ensure user owns this purchase
      },
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        },
        package: true
      }
    })

    if (!purchase) {
      return NextResponse.json({ error: "Purchase not found" }, { status: 404 })
    }

    if (purchase.status !== PaymentStatus.COMPLETED) {
      return NextResponse.json({ 
        error: "Receipt only available for completed purchases" 
      }, { status: 400 })
    }

    // Generate receipt data
    const receiptData = {
      purchase: {
        id: purchase.id,
        date: purchase.createdAt,
        status: purchase.status,
        stripePaymentId: purchase.stripePaymentId
      },
      customer: {
        name: purchase.user.name || 'N/A',
        email: purchase.user.email
      },
      package: {
        name: purchase.package.name,
        description: purchase.package.description
      },
      items: [
        {
          description: `${purchase.package.name} - ${purchase.credits} Credits`,
          quantity: 1,
          unitPrice: purchase.amount,
          total: purchase.amount
        }
      ],
      bonusCredits: purchase.bonusCredits,
      totals: {
        subtotal: purchase.amount,
        tax: 0, // Assuming no tax for digital credits
        total: purchase.amount,
        credits: purchase.credits,
        bonusCredits: purchase.bonusCredits,
        totalCredits: purchase.totalCredits
      },
      company: {
        name: "Black Blogs",
        address: "123 Digital Street",
        city: "Tech City, TC 12345",
        email: "<EMAIL>",
        website: "https://blackblogs.com"
      }
    }

    // For now, return JSON receipt data
    // In a real implementation, you might generate a PDF using libraries like puppeteer or jsPDF
    return NextResponse.json({
      receipt: receiptData,
      downloadUrl: `/api/credits/purchases/${params.id}/receipt/pdf` // Future PDF endpoint
    })

  } catch (error) {
    console.error("Error generating receipt:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
