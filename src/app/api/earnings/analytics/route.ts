import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { UserRole, EarningType } from "@prisma/client"

// GET /api/earnings/analytics - Get earnings analytics for the author
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.AUTHOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id

    // Get total earnings
    const totalEarningsResult = await prisma.earning.aggregate({
      where: { userId },
      _sum: { authorEarning: true }
    })

    // Get current month earnings
    const currentMonth = new Date()
    currentMonth.setDate(1)
    currentMonth.setHours(0, 0, 0, 0)

    const monthlyEarningsResult = await prisma.earning.aggregate({
      where: {
        userId,
        createdAt: { gte: currentMonth }
      },
      _sum: { authorEarning: true }
    })

    // Get pending payouts
    const pendingPayoutsResult = await prisma.earning.aggregate({
      where: {
        userId,
        isPaidOut: false
      },
      _sum: { authorEarning: true }
    })

    // Get completed payouts
    const completedPayoutsResult = await prisma.payout.aggregate({
      where: {
        userId,
        status: 'COMPLETED'
      },
      _sum: { amount: true }
    })

    // Get earnings by type
    const earningsByType = await prisma.earning.groupBy({
      by: ['type'],
      where: { userId },
      _sum: { authorEarning: true }
    })

    // Get earnings by month for the last 12 months
    const twelveMonthsAgo = new Date()
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12)

    const earningsByMonth = await prisma.$queryRaw`
      SELECT 
        DATE_TRUNC('month', created_at) as month,
        SUM(author_earning) as amount
      FROM earnings 
      WHERE user_id = ${userId} 
        AND created_at >= ${twelveMonthsAgo}
      GROUP BY DATE_TRUNC('month', created_at)
      ORDER BY month ASC
    ` as Array<{ month: Date; amount: number }>

    // Format earnings by type
    const earningsByTypeFormatted = Object.values(EarningType).reduce((acc, type) => {
      const earning = earningsByType.find(e => e.type === type)
      acc[type] = Number(earning?._sum.authorEarning || 0)
      return acc
    }, {} as Record<EarningType, number>)

    // Format earnings by month
    const earningsByMonthFormatted = earningsByMonth.map(item => ({
      month: item.month.toISOString().slice(0, 7), // YYYY-MM format
      amount: Number(item.amount
    }))

    const analytics = {
      totalEarnings: Number(totalEarningsResult._sum.authorEarning || 0),
      monthlyEarnings: Number(monthlyEarningsResult._sum.authorEarning || 0),
      pendingPayouts: Number(pendingPayoutsResult._sum.authorEarning || 0),
      completedPayouts: Number(completedPayoutsResult._sum.amount || 0),
      earningsByType: earningsByTypeFormatted,
      earningsByMonth: earningsByMonthFormatted
    }

    return NextResponse.json(analytics)
  } catch (error) {
    console.error("Error fetching earnings analytics:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
