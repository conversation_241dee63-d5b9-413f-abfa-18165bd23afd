import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { UserRole } from "@prisma/client"
import { z } from "zod"

const updateCreditPackageSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().min(1).max(500).optional(),
  credits: z.number().int().min(1).max(10000).optional(),
  bonusCredits: z.number().int().min(0).max(5000).optional(),
  price: z.number().min(0.01).max(1000).optional(),
  currency: z.string().length(3).optional(),
  sortOrder: z.number().int().min(1).max(100).optional(),
  isActive: z.boolean().optional(),
  isPopular: z.boolean().optional(),
  isBestValue: z.boolean().optional(),
})

// GET /api/admin/credit-packages/[id] - Get specific credit package (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const creditPackage = await prisma.creditPackage.findUnique({
      where: { id: params.id },
      include: {
        purchases: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        _count: {
          select: {
            purchases: true
          }
        }
      }
    })

    if (!creditPackage) {
      return NextResponse.json({ error: "Package not found" }, { status: 404 })
    }

    // Calculate metrics
    const totalRevenue = await prisma.creditPurchase.aggregate({
      where: {
        packageId: params.id,
        status: 'COMPLETED'
      },
      _sum: {
        amount: true
      }
    })

    const recentPurchases = await prisma.creditPurchase.count({
      where: {
        packageId: params.id,
        status: 'COMPLETED',
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      }
    })

    const packageWithMetrics = {
      ...creditPackage,
      totalPurchases: creditPackage._count.purchases,
      totalRevenue: totalRevenue._sum.amount || 0,
      recentPurchases,
      valuePerCredit: Number(creditPackage.price / (creditPackage.credits + creditPackage.bonusCredits),
      bonusPercentage: creditPackage.bonusCredits > 0 ? Math.round((creditPackage.bonusCredits / creditPackage.credits) * 100) : 0
    }

    return NextResponse.json({ package: packageWithMetrics })
  } catch (error) {
    console.error("Error fetching credit package:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/admin/credit-packages/[id] - Update credit package (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const updateData = updateCreditPackageSchema.parse(body)

    // Check if package exists
    const existingPackage = await prisma.creditPackage.findUnique({
      where: { id: params.id }
    })

    if (!existingPackage) {
      return NextResponse.json({ error: "Package not found" }, { status: 404 })
    }

    // Check if name already exists (if updating name)
    if (updateData.name && updateData.name !== existingPackage.name) {
      const nameExists = await prisma.creditPackage.findFirst({
        where: { 
          name: updateData.name,
          id: { not: params.id }
        }
      })

      if (nameExists) {
        return NextResponse.json(
          { error: "Package name already exists" },
          { status: 400 }
        )
      }
    }

    // Handle exclusive flags (commented out until schema is updated)
    // if (updateData.isPopular === true) {
    //   await prisma.creditPackage.updateMany({
    //     where: { 
    //       isPopular: true,
    //       id: { not: params.id }
    //     },
    //     data: { isPopular: false }
    //   })
    // }

    // if (updateData.isBestValue === true) {
    //   await prisma.creditPackage.updateMany({
    //     where: { 
    //       isBestValue: true,
    //       id: { not: params.id }
    //     },
    //     data: { isBestValue: false }
    //   })
    // }

    const updatedPackage = await prisma.creditPackage.update({
      where: { id: params.id },
      data: updateData
    })

    return NextResponse.json({ package: updatedPackage })
  } catch (error) {
    console.error("Error updating credit package:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/credit-packages/[id] - Delete credit package (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if package exists
    const existingPackage = await prisma.creditPackage.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            purchases: true
          }
        }
      }
    })

    if (!existingPackage) {
      return NextResponse.json({ error: "Package not found" }, { status: 404 })
    }

    // Check if package has purchases
    if (existingPackage._count.purchases > 0) {
      return NextResponse.json(
        { 
          error: "Cannot delete package with existing purchases. Deactivate instead.",
          hasPurchases: true
        },
        { status: 400 }
      )
    }

    await prisma.creditPackage.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting credit package:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
