import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get("page") || "1")
  const limit = parseInt(searchParams.get("limit") || "12")
  const genre = searchParams.get("genre")
  const search = searchParams.get("search")
  const status = searchParams.get("status") || "PUBLISHED"

  const skip = (page - 1) * limit

  const where = {
    status: status,
    ...(genre && { genre }),
    ...(search && {
      OR: [
        { title: { contains: search, mode: "insensitive" as const } },
        { description: { contains: search, mode: "insensitive" as const } },
        { author: { name: { contains: search, mode: "insensitive" as const } } },
      ],
    }),
  }

  try {
    // Build the query for Supabase - simplified first
    let query = supabase
      .from('novels')
      .select('*')
      .eq('status', status)
      .order('created_at', { ascending: false })
      .range(skip, skip + limit - 1)

    // Add genre filter if provided
    if (genre) {
      query = query.eq('genre', genre)
    }

    // Add search filter if provided
    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%,author.name.ilike.%${search}%`)
    }

    const { data: novels, error: novelsError } = await query

    if (novelsError) {
      console.error("Error fetching novels:", novelsError)
      throw novelsError
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('novels')
      .select('*', { count: 'exact', head: true })
      .eq('status', status)

    if (genre) {
      countQuery = countQuery.eq('genre', genre)
    }

    if (search) {
      countQuery = countQuery.or(`title.ilike.%${search}%,description.ilike.%${search}%`)
    }

    const { count: total, error: countError } = await countQuery

    if (countError) {
      console.error("Error counting novels:", countError)
      throw countError
    }

    // Transform snake_case to camelCase and add missing fields
    const transformedNovels = novels?.map(novel => ({
      ...novel,
      // Convert snake_case to camelCase for consistency with frontend
      createdAt: novel.created_at,
      updatedAt: novel.updated_at,
      authorId: novel.author_id,
      // Add mock author data for now
      author: { id: novel.author_id, name: "Unknown", image: null },
      _count: {
        chapters: 0 // We'll get this separately later
      }
    })) || []

    return NextResponse.json({
      novels: transformedNovels,
      pagination: {
        page,
        limit,
        total: total || 0,
        pages: Math.ceil((total || 0) / limit),
      },
    })
  } catch (error) {
    console.error("Error fetching novels:", error)
    return NextResponse.json(
      { error: "Failed to fetch novels" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "AUTHOR") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const { title, description, synopsis, genre, tags } = body

    // Validate required fields
    if (!title || title.trim().length === 0) {
      return NextResponse.json(
        { error: "Title is required" },
        { status: 400 }
      )
    }

    const { data: novel, error } = await supabase
      .from('novels')
      .insert({
        title: title.trim(),
        description: description?.trim() || null,
        synopsis: synopsis?.trim() || null,
        genre: genre?.trim() || null,
        tags: tags || [],
        authorId: session.user.id,
        status: "DRAFT",
      })
      .select(`
        *,
        author:users!authorId(id, name, image)
      `)
      .single()

    if (error) {
      console.error("Error creating novel:", error)
      throw error
    }

    // Add chapter count (new novels have 0 chapters)
    const novelWithCount = {
      ...novel,
      _count: { chapters: 0 }
    }

    return NextResponse.json(novelWithCount, { status: 201 })
  } catch (error) {
    console.error("Error creating novel:", error)
    return NextResponse.json(
      { error: "Failed to create novel" },
      { status: 500 }
    )
  }
}