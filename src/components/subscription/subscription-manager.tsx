"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { 
  useGetCurrentSubscriptionQuery, 
  useCancelSubscriptionMutation,
  useReactivateSubscriptionMutation 
} from "@/store/api/subscriptionsApi"
import { formatCurrency } from "@/lib/stripe"
import { Crown, Calendar, CreditCard, AlertTriangle } from "lucide-react"
import { format } from "date-fns"

export function SubscriptionManager() {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const { data: subscriptionData, isLoading: isLoadingSubscription } = useGetCurrentSubscriptionQuery()
  const [cancelSubscription] = useCancelSubscriptionMutation()
  const [reactivateSubscription] = useReactivateSubscriptionMutation()

  const subscription = subscriptionData?.subscription

  const handleCancelSubscription = async (immediate = false) => {
    if (!subscription) return

    setIsLoading(true)
    try {
      const result = await cancelSubscription({
        cancelAtPeriodEnd: !immediate
      }).unwrap()

      toast({
        title: "Subscription Updated",
        description: result.message,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to cancel subscription. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleReactivateSubscription = async () => {
    if (!subscription) return

    setIsLoading(true)
    try {
      const result = await reactivateSubscription().unwrap()

      toast({
        title: "Subscription Reactivated",
        description: "Your subscription has been reactivated successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reactivate subscription. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!session) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            Please sign in to manage your subscription.
          </p>
        </CardContent>
      </Card>
    )
  }

  if (isLoadingSubscription) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!subscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Subscription
          </CardTitle>
          <CardDescription>
            You don't have an active subscription. Upgrade to access premium content.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Badge variant="secondary">Free Tier</Badge>
          <div className="mt-4">
            <Button asChild>
              <a href="/pricing">Upgrade to Premium</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const getStatusBadge = () => {
    switch (subscription.status) {
      case 'ACTIVE':
        return <Badge variant="default">Active</Badge>
      case 'TRIALING':
        return <Badge variant="secondary">Trial</Badge>
      case 'CANCELED':
        return <Badge variant="destructive">Canceled</Badge>
      case 'PAST_DUE':
        return <Badge variant="destructive">Past Due</Badge>
      default:
        return <Badge variant="outline">{subscription.status}</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Crown className="h-5 w-5" />
          Subscription
        </CardTitle>
        <CardDescription>
          Manage your subscription and billing information.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Plan */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold">{subscription.tierConfig?.name || subscription.tier}</h3>
            {getStatusBadge()}
          </div>
          <p className="text-sm text-muted-foreground mb-4">
            {subscription.tierConfig?.description}
          </p>
          
          {subscription.tierConfig?.price && (
            <p className="text-2xl font-bold">
              {formatCurrency(Number(subscription.tierConfig.price)}
              <span className="text-sm font-normal text-muted-foreground">/month</span>
            </p>
          )}
        </div>

        <Separator />

        {/* Billing Information */}
        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Billing Information
          </h4>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Current Period</p>
              <p>{format(new Date(subscription.currentPeriodStart), 'MMM d, yyyy')} - {format(new Date(subscription.currentPeriodEnd), 'MMM d, yyyy')}</p>
            </div>
            
            {subscription.trialEnd && new Date(subscription.trialEnd) > new Date() && (
              <div>
                <p className="text-muted-foreground">Trial Ends</p>
                <p>{format(new Date(subscription.trialEnd), 'MMM d, yyyy')}</p>
              </div>
            )}
          </div>

          {subscription.cancelAtPeriodEnd && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <p className="text-sm text-yellow-800">
                Your subscription will be canceled on {format(new Date(subscription.currentPeriodEnd), 'MMM d, yyyy')}.
              </p>
            </div>
          )}
        </div>

        <Separator />

        {/* Actions */}
        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Manage Subscription
          </h4>
          
          <div className="flex gap-2">
            {subscription.cancelAtPeriodEnd ? (
              <Button 
                onClick={handleReactivateSubscription}
                disabled={isLoading}
                variant="default"
              >
                Reactivate Subscription
              </Button>
            ) : (
              <Button 
                onClick={() => handleCancelSubscription(false)}
                disabled={isLoading}
                variant="outline"
              >
                Cancel at Period End
              </Button>
            )}
            
            <Button 
              onClick={() => handleCancelSubscription(true)}
              disabled={isLoading}
              variant="destructive"
            >
              Cancel Immediately
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
