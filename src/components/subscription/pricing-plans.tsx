"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { useGetSubscriptionTiersQuery, useGetCurrentSubscriptionQuery } from "@/store/api/subscriptionsApi"
import { formatCurrency } from "@/lib/stripe"
import { Check, Crown, Star, Zap } from "lucide-react"
import { SubscriptionTier } from "@prisma/client"

interface PricingPlansProps {
  onSelectPlan?: (tier: SubscriptionTier, isYearly: boolean) => void
}

export function PricingPlans({ onSelectPlan }: PricingPlansProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [isYearly, setIsYearly] = useState(false)

  const { data: tiersData, isLoading: isLoadingTiers } = useGetSubscriptionTiersQuery()
  const { data: subscriptionData } = useGetCurrentSubscriptionQuery()

  const tiers = tiersData?.tiers || []
  const currentSubscription = subscriptionData?.subscription

  const getTierIcon = (tier: SubscriptionTier) => {
    switch (tier) {
      case 'FREE':
        return <Star className="h-6 w-6" />
      case 'PREMIUM':
        return <Crown className="h-6 w-6" />
      case 'PREMIUM_PLUS':
        return <Zap className="h-6 w-6" />
      default:
        return <Star className="h-6 w-6" />
    }
  }

  const getTierColor = (tier: SubscriptionTier) => {
    switch (tier) {
      case 'FREE':
        return 'border-gray-200'
      case 'PREMIUM':
        return 'border-blue-200 bg-blue-50'
      case 'PREMIUM_PLUS':
        return 'border-purple-200 bg-purple-50'
      default:
        return 'border-gray-200'
    }
  }

  const getPrice = (tier: any) => {
    if (tier.tier === 'FREE') return 0
    return isYearly ? Number(tier.yearlyPrice || tier.price * 12) : Number(tier.price
  }

  const getYearlySavings = (tier: any) => {
    if (tier.tier === 'FREE' || !tier.yearlyPrice) return 0
    const monthlyTotal = Number(tier.price * 12
    const yearlyPrice = Number(tier.yearlyPrice)
    return monthlyTotal - yearlyPrice
  }

  const handleSelectPlan = (tier: SubscriptionTier) => {
    if (!session) {
      toast({
        title: "Sign In Required",
        description: "Please sign in to subscribe to a plan.",
        variant: "destructive",
      })
      return
    }

    if (tier === 'FREE') {
      toast({
        title: "Already on Free Plan",
        description: "You're already on the free plan.",
      })
      return
    }

    if (onSelectPlan) {
      onSelectPlan(tier, isYearly)
    }
  }

  const isCurrentPlan = (tier: SubscriptionTier) => {
    return currentSubscription?.tier === tier && currentSubscription.status === 'ACTIVE'
  }

  if (isLoadingTiers) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Billing Toggle */}
      <div className="flex items-center justify-center space-x-4">
        <Label htmlFor="billing-toggle" className={!isYearly ? "font-semibold" : ""}>
          Monthly
        </Label>
        <Switch
          id="billing-toggle"
          checked={isYearly}
          onCheckedChange={setIsYearly}
        />
        <Label htmlFor="billing-toggle" className={isYearly ? "font-semibold" : ""}>
          Yearly
        </Label>
        {isYearly && (
          <Badge variant="secondary" className="ml-2">
            Save up to 20%
          </Badge>
        )}
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {tiers.map((tier) => {
          const price = getPrice(tier)
          const savings = getYearlySavings(tier)
          const isCurrent = isCurrentPlan(tier.tier)

          return (
            <Card 
              key={tier.id} 
              className={`relative ${getTierColor(tier.tier)} ${
                tier.tier === 'PREMIUM' ? 'ring-2 ring-blue-500' : ''
              }`}
            >
              {tier.tier === 'PREMIUM' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500 text-white">Most Popular</Badge>
                </div>
              )}

              <CardHeader className="text-center">
                <div className="flex justify-center mb-2">
                  {getTierIcon(tier.tier)}
                </div>
                <CardTitle className="text-2xl">{tier.name}</CardTitle>
                <CardDescription>{tier.description}</CardDescription>
                
                <div className="mt-4">
                  <div className="text-4xl font-bold">
                    {tier.tier === 'FREE' ? 'Free' : formatCurrency(price)}
                  </div>
                  {tier.tier !== 'FREE' && (
                    <div className="text-sm text-muted-foreground">
                      per {isYearly ? 'year' : 'month'}
                    </div>
                  )}
                  {isYearly && savings > 0 && (
                    <div className="text-sm text-green-600 font-medium">
                      Save {formatCurrency(savings)} per year
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Features */}
                <ul className="space-y-2">
                  {tier.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* Action Button */}
                <div className="pt-4">
                  {isCurrent ? (
                    <Button disabled className="w-full">
                      Current Plan
                    </Button>
                  ) : (
                    <Button 
                      onClick={() => handleSelectPlan(tier.tier)}
                      className="w-full"
                      variant={tier.tier === 'FREE' ? 'outline' : 'default'}
                    >
                      {tier.tier === 'FREE' ? 'Current Plan' : 'Subscribe'}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Additional Info */}
      <div className="text-center text-sm text-muted-foreground">
        <p>All plans include a 7-day free trial. Cancel anytime.</p>
        <p className="mt-1">Prices are in USD and exclude applicable taxes.</p>
      </div>
    </div>
  )
}
