"use client"

import React, { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useDispatch, useSelector } from "react-redux"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import {
  Coins,
  CreditCard,
  Check,
  Zap,
  TrendingUp,
  Star,
  Gift,
  AlertCircle,
  Wallet,
  History,
  RefreshCw
} from "lucide-react"
import { formatCredits, formatCreditPrice, creditsToUSD } from "@/lib/credits"
import { loadStripe } from "@stripe/stripe-js"
import {
  useGetCreditPackagesQuery,
  usePurchaseCreditsMutation,
  useGetCreditBalanceQuery,
  useGetCreditTransactionsQuery
} from "@/store/api/creditsApi"
import {
  selectPurchaseModal,
  closePurchaseModal,
  setSelectedPackage,
  handleSuccessfulPurchase
} from "@/store/slices/creditSlice"
import type { AppDispatch } from "@/lib/store"

interface CreditPackage {
  id: string
  name: string
  description: string
  credits: number
  bonusCredits: number
  price: number
  currency: string
  sortOrder: number
}

interface PurchaseRecommendation {
  packageId: string
  reason: string
  savings?: number
  isRecommended: boolean
}

export function CreditPurchaseModal() {
  const { data: session } = useSession()
  const { toast } = useToast()
  const dispatch = useDispatch<AppDispatch>()
  const [showTransactionHistory, setShowTransactionHistory] = useState(false)

  // Redux state
  const { isOpen, selectedPackageId } = useSelector(selectPurchaseModal)

  // Debug logging
  console.log('CreditPurchaseModal render - isOpen:', isOpen, 'selectedPackageId:', selectedPackageId)

  // Track state changes
  useEffect(() => {
    console.log('CreditPurchaseModal state changed - isOpen:', isOpen)
  }, [isOpen])

  // RTK Query
  const { data: packagesData, isLoading } = useGetCreditPackagesQuery()
  const { data: balanceData, refetch: refetchBalance } = useGetCreditBalanceQuery(undefined, {
    skip: !session?.user,
  })
  const { data: transactionsData } = useGetCreditTransactionsQuery(
    { page: 1, limit: 5, type: 'CREDIT' },
    { skip: !session?.user || !showTransactionHistory }
  )
  const [purchaseCredits, { isLoading: isPurchasing }] = usePurchaseCreditsMutation()

  const packages = packagesData?.packages || []
  const currentBalance = balanceData?.balance ?? 0
  const recentTransactions = transactionsData?.transactions || []

  // Generate purchase recommendations based on user's current balance and usage
  const generateRecommendations = (): PurchaseRecommendation[] => {
    const recommendations: PurchaseRecommendation[] = []

    packages.forEach(pkg => {
      let reason = ""
      let isRecommended = false
      let savings = 0

      // Calculate value per credit
      const valuePerCredit = pkg.price / (pkg.credits + pkg.bonusCredits)
      const baseValuePerCredit = packages[0]?.price / packages[0]?.credits || 0.1

      if (pkg.bonusCredits > 0) {
        const bonusPercentage = Math.round((pkg.bonusCredits / pkg.credits) * 100)
        savings = pkg.bonusCredits * 0.1 // Assuming $0.10 per credit
        reason = `${bonusPercentage}% bonus credits (save $${savings.toFixed(2)})`
        isRecommended = bonusPercentage >= 15
      } else if (currentBalance < 20) {
        reason = "Perfect starter pack for new readers"
        isRecommended = pkg.credits <= 100
      } else if (currentBalance < 50) {
        reason = "Great value for regular reading"
        isRecommended = pkg.credits >= 100 && pkg.credits <= 300
      } else {
        reason = "Best value for avid readers"
        isRecommended = pkg.credits >= 300
      }

      recommendations.push({
        packageId: pkg.id,
        reason,
        savings,
        isRecommended
      })
    })

    return recommendations
  }

  const recommendations = generateRecommendations()

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      dispatch(closePurchaseModal())
      setShowTransactionHistory(false)
    }
  }

  const handlePurchase = async (packageId: string) => {
    if (!session) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to purchase credits",
        variant: "destructive"
      })
      return
    }

    dispatch(setSelectedPackage(packageId))

    try {
      const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)
      if (!stripe) throw new Error('Stripe not loaded')

      // Create a test payment method (in production, collect from user)
      const { paymentMethod, error: pmError } = await stripe.createPaymentMethod({
        type: 'card',
        card: {
          number: '****************',
          exp_month: 12,
          exp_year: 2025,
          cvc: '123',
        },
      })

      if (pmError || !paymentMethod) {
        throw new Error(pmError?.message || 'Failed to create payment method')
      }

      // Purchase credits using RTK Query
      const result = await purchaseCredits({
        packageId,
        paymentMethodId: paymentMethod.id,
      }).unwrap()

      if (result.requiresAction && result.clientSecret) {
        const { error: confirmError } = await stripe.confirmCardPayment(result.clientSecret)
        if (confirmError) {
          throw new Error(confirmError.message)
        }
      }

      toast({
        title: "Credits Purchased!",
        description: `Successfully purchased ${formatCredits(result.purchase.totalCredits)}. Your new balance: ${formatCredits(currentBalance + result.purchase.totalCredits)}`,
      })

      // Update Redux state and refresh balance
      dispatch(handleSuccessfulPurchase(result.purchase.totalCredits, {
        id: result.purchase.id,
        description: `Purchased ${result.purchase.credits} credits${result.purchase.bonusCredits > 0 ? ` + ${result.purchase.bonusCredits} bonus` : ''} (${result.purchase.package.name})`,
        createdAt: new Date().toISOString(),
      }))

      // Refresh balance to show updated amount
      refetchBalance()

      // Close modal after successful purchase
      setTimeout(() => {
        dispatch(closePurchaseModal())
      }, 2000)

    } catch (error: any) {
      console.error('Purchase error:', error)
      toast({
        title: "Purchase Failed",
        description: error.message || "Failed to purchase credits. Please try again.",
        variant: "destructive",
      })
    } finally {
      dispatch(setSelectedPackage(null))
    }
  }

  const getPackageBadges = (pkg: CreditPackage, index: number) => {
    const recommendation = recommendations.find(r => r.packageId === pkg.id)
    const badges = []

    // Popular badge for middle packages
    if (index === 1 && packages.length >= 3) {
      badges.push(
        <Badge key="popular" className="absolute -top-2 -right-2 bg-blue-500 hover:bg-blue-600">
          <Star className="h-3 w-3 mr-1" />
          Popular
        </Badge>
      )
    }

    // Best value badge for packages with highest bonus percentage
    if (pkg.bonusCredits > 0) {
      const bonusPercentage = Math.round((pkg.bonusCredits / pkg.credits) * 100)
      if (bonusPercentage >= 20) {
        badges.push(
          <Badge key="best-value" className="absolute -top-2 -left-2 bg-green-500 hover:bg-green-600">
            <TrendingUp className="h-3 w-3 mr-1" />
            Best Value
          </Badge>
        )
      }
    }

    // Recommended badge
    if (recommendation?.isRecommended) {
      badges.push(
        <Badge key="recommended" className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-purple-500 hover:bg-purple-600">
          <Gift className="h-3 w-3 mr-1" />
          Recommended
        </Badge>
      )
    }

    return badges
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-5xl max-h-[85vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5 text-yellow-500" />
            Purchase Credits
          </DialogTitle>
          <DialogDescription>
            Choose a credit package to unlock premium content. Credits never expire and support your favorite authors.
          </DialogDescription>
        </DialogHeader>

        {/* Current Balance & Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500 rounded-full">
                  <Wallet className="h-4 w-4 text-white" />
                </div>
                <div>
                  <p className="text-sm text-blue-600 font-medium">Current Balance</p>
                  <p className="text-lg font-bold text-blue-800">{formatCredits(currentBalance)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-500 rounded-full">
                  <TrendingUp className="h-4 w-4 text-white" />
                </div>
                <div>
                  <p className="text-sm text-green-600 font-medium">Equivalent Value</p>
                  <p className="text-lg font-bold text-green-800">${creditsToUSD(currentBalance).toFixed(2)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-500 rounded-full">
                  <History className="h-4 w-4 text-white" />
                </div>
                <div>
                  <p className="text-sm text-purple-600 font-medium">Recent Activity</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-purple-800 font-bold p-0 h-auto"
                    onClick={() => setShowTransactionHistory(!showTransactionHistory)}
                  >
                    View History
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Transaction History */}
        {showTransactionHistory && (
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-sm">Recent Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              {recentTransactions.length > 0 ? (
                <div className="space-y-2">
                  {recentTransactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div>
                        <p className="text-sm font-medium">{transaction.description}</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(transaction.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div className={`text-sm font-bold ${transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {transaction.amount > 0 ? '+' : ''}{formatCredits(transaction.amount)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">No recent transactions</p>
              )}
            </CardContent>
          </Card>
        )}

        {/* Low Balance Warning */}
        {currentBalance < 20 && (
          <Alert className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Your credit balance is running low. Consider purchasing a larger package for better value and uninterrupted reading.
            </AlertDescription>
          </Alert>
        )}

        {/* Credit Packages */}
        <div className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Choose Your Package</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => refetchBalance()}
              className="text-muted-foreground"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Balance
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {isLoading ? (
              Array.from({ length: 4 }).map((_, i) => (
                <Card key={i} className="relative">
                  <CardHeader>
                    <Skeleton className="h-6 w-24" />
                    <Skeleton className="h-4 w-32" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-8 w-16 mb-2" />
                    <Skeleton className="h-4 w-20 mb-4" />
                    <Skeleton className="h-10 w-full" />
                  </CardContent>
                </Card>
              ))
            ) : (
              packages.map((pkg, index) => {
                const recommendation = recommendations.find(r => r.packageId === pkg.id)
                const totalCredits = pkg.credits + pkg.bonusCredits
                const valuePerCredit = pkg.price / totalCredits

                return (
                  <Card
                    key={pkg.id}
                    className={`relative cursor-pointer transition-all hover:shadow-lg border-2 ${
                      selectedPackageId === pkg.id
                        ? 'ring-2 ring-blue-500 border-blue-300'
                        : recommendation?.isRecommended
                          ? 'border-purple-200 bg-purple-50/30'
                          : 'border-gray-200'
                    }`}
                  >
                    {getPackageBadges(pkg, index)}

                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">{pkg.name}</CardTitle>
                      <CardDescription className="text-sm">
                        {pkg.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent>
                      <div className="space-y-3">
                        <div className="text-center">
                          <div className="text-2xl font-bold">
                            ${pkg.price}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {formatCredits(pkg.credits)}
                            {pkg.bonusCredits > 0 && (
                              <span className="text-green-600 font-medium">
                                {" "}+ {formatCredits(pkg.bonusCredits)} bonus
                              </span>
                            )}
                          </div>
                          <div className="text-xs text-muted-foreground mt-1">
                            ${valuePerCredit.toFixed(3)} per credit
                          </div>
                        </div>

                        {pkg.bonusCredits > 0 && (
                          <div className="flex items-center justify-center gap-1 text-xs text-green-600 bg-green-50 rounded-full py-1 px-2">
                            <Zap className="h-3 w-3" />
                            {Math.round((pkg.bonusCredits / pkg.credits) * 100)}% bonus
                            {recommendation?.savings && (
                              <span className="ml-1 font-medium">
                                (Save ${recommendation.savings.toFixed(2)})
                              </span>
                            )}
                          </div>
                        )}

                        {recommendation && (
                          <div className="text-xs text-center text-muted-foreground bg-gray-50 rounded p-2">
                            {recommendation.reason}
                          </div>
                        )}

                        <Button
                          onClick={() => handlePurchase(pkg.id)}
                          disabled={isPurchasing}
                          className="w-full"
                          variant={selectedPackageId === pkg.id ? "default" : recommendation?.isRecommended ? "default" : "outline"}
                        >
                          {isPurchasing && selectedPackageId === pkg.id ? (
                            <>
                              <CreditCard className="h-4 w-4 mr-2 animate-pulse" />
                              Processing...
                            </>
                          ) : (
                            <>
                              <CreditCard className="h-4 w-4 mr-2" />
                              Purchase
                            </>
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })
            )}
          </div>
        </div>

        <Separator className="my-6" />

        {/* Benefits and Features */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200">
            <div className="flex items-start gap-3">
              <Check className="h-5 w-5 text-green-600 mt-0.5" />
              <div className="text-sm text-green-800">
                <p className="font-medium mb-2">What you get:</p>
                <ul className="space-y-1">
                  <li>• Instant access to premium content</li>
                  <li>• Credits never expire</li>
                  <li>• Support your favorite authors</li>
                  <li>• Secure payment processing</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200">
            <div className="flex items-start gap-3">
              <Coins className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-2">Credit Usage:</p>
                <ul className="space-y-1">
                  <li>• Typical chapter: 3-5 credits</li>
                  <li>• Premium chapter: 5-10 credits</li>
                  <li>• Full novel access: 20-50 credits</li>
                  <li>• 70% goes directly to authors</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Security Notice */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg border">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <CreditCard className="h-4 w-4" />
            <span>Secure payment processing powered by Stripe. Your payment information is never stored on our servers.</span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
