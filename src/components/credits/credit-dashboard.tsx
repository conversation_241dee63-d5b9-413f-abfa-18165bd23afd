"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { 
  Coins, 
  TrendingUp, 
  DollarSign, 
  Users,
  BookOpen,
  Calendar
} from "lucide-react"
import { formatCredits, creditsToUSD } from "@/lib/credits"
import { formatCurrency } from "@/lib/stripe"
import { CreditBalance } from "./credit-balance"
import { CreditTransactionHistory } from "./credit-transaction-history"

interface CreditStats {
  totalCreditsEarned: number
  totalCreditsSold: number
  totalRevenueFromCredits: number
  creditPurchasesThisMonth: number
  averageCreditsPerPurchase: number
}

interface CreditDashboardProps {
  className?: string
}

export function CreditDashboard({ className }: CreditDashboardProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [creditBalance, setCreditBalance] = useState<number>(0)
  const [creditStats, setCreditStats] = useState<CreditStats | null>(null)
  const [isLoadingBalance, setIsLoadingBalance] = useState(false)
  const [isLoadingStats, setIsLoadingStats] = useState(false)

  useEffect(() => {
    if (session) {
      loadCreditBalance()
      if (session.user.role === 'AUTHOR') {
        loadCreditStats()
      }
    }
  }, [session])

  const loadCreditBalance = async () => {
    setIsLoadingBalance(true)
    try {
      const response = await fetch('/api/credits/balance')
      if (!response.ok) throw new Error('Failed to load balance')
      
      const data = await response.json()
      setCreditBalance(data.balance || 0)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load credit balance",
        variant: "destructive"
      })
    } finally {
      setIsLoadingBalance(false)
    }
  }

  const loadCreditStats = async () => {
    setIsLoadingStats(true)
    try {
      const response = await fetch('/api/earnings?type=CREDIT_PURCHASE&limit=1000')
      if (!response.ok) throw new Error('Failed to load stats')
      
      const data = await response.json()
      const earnings = data.earnings || []
      
      // Calculate stats from earnings
      const totalCreditsEarned = earnings.reduce((sum: number, earning: any) => {
        return sum + (creditsToUSD(earning.amount / 0.10) // Convert back to credits
      }, 0)
      
      const totalRevenueFromCredits = earnings.reduce((sum: number, earning: any) => {
        return sum + earning.authorEarning
      }, 0)
      
      // Get current month earnings
      const currentMonth = new Date().getMonth()
      const currentYear = new Date().getFullYear()
      const thisMonthEarnings = earnings.filter((earning: any) => {
        const earningDate = new Date(earning.createdAt)
        return earningDate.getMonth() === currentMonth && earningDate.getFullYear() === currentYear
      })
      
      setCreditStats({
        totalCreditsEarned: Math.round(totalCreditsEarned),
        totalCreditsSold: earnings.length,
        totalRevenueFromCredits,
        creditPurchasesThisMonth: thisMonthEarnings.length,
        averageCreditsPerPurchase: earnings.length > 0 ? Math.round(totalCreditsEarned / earnings.length) : 0
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load credit statistics",
        variant: "destructive"
      })
    } finally {
      setIsLoadingStats(false)
    }
  }

  if (!session) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center text-muted-foreground">
            <Coins className="h-5 w-5 mr-2" />
            Sign in to view credit dashboard
          </div>
        </CardContent>
      </Card>
    )
  }

  const isAuthor = session.user.role === 'AUTHOR'

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Credit Balance */}
      <CreditBalance
        balance={creditBalance}
        isLoading={isLoadingBalance}
        onRefresh={loadCreditBalance}
      />

      {/* Author Stats */}
      {isAuthor && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Credit Earnings Overview
            </CardTitle>
            <CardDescription>
              Your earnings from readers purchasing your content with credits
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingStats ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                ))}
              </div>
            ) : creditStats ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(creditStats.totalRevenueFromCredits)}
                  </p>
                </div>
                
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Credits Sold</p>
                  <p className="text-2xl font-bold">
                    {formatCredits(creditStats.totalCreditsEarned)}
                  </p>
                </div>
                
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Total Purchases</p>
                  <p className="text-2xl font-bold">
                    {creditStats.totalCreditsSold}
                  </p>
                </div>
                
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">This Month</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {creditStats.creditPurchasesThisMonth}
                  </p>
                </div>
                
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Avg. Credits/Purchase</p>
                  <p className="text-2xl font-bold">
                    {formatCredits(creditStats.averageCreditsPerPurchase)}
                  </p>
                </div>
                
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Revenue Share</p>
                  <Badge variant="outline" className="text-lg font-bold">
                    70%
                  </Badge>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <DollarSign className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No credit earnings yet</p>
                <p className="text-sm">Start creating premium content to earn from credit purchases!</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Tabs for different views */}
      <Tabs defaultValue="transactions" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="transactions">Transaction History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="transactions" className="space-y-4">
          <CreditTransactionHistory />
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Credit Usage Analytics
              </CardTitle>
              <CardDescription>
                Detailed insights into your credit usage patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Analytics coming soon</p>
                <p className="text-sm">We're working on detailed analytics for your credit usage.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
