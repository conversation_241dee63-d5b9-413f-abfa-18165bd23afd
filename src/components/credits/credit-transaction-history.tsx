"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import {
  History,
  Plus,
  Minus,
  RefreshCw,
  ShoppingCart,
  Book,
  Gift,
  Settings,
  ChevronLeft,
  ChevronRight,
  Download,
  Search,
  Filter,
  Calendar,
  Receipt,
  TrendingUp,
  DollarSign
} from "lucide-react"
import { formatCredits, creditsToUSD } from "@/lib/credits"
import { format, startOfMonth, endOfMonth, subMonths } from "date-fns"
import { useGetCreditTransactionsQuery } from "@/store/api/creditsApi"

interface CreditTransaction {
  id: string
  type: 'PURCHASE' | 'SPEND' | 'REFUND' | 'BONUS' | 'ADMIN_ADJUSTMENT'
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELED'
  amount: number
  description: string
  balanceBefore: number
  balanceAfter: number
  createdAt: string
  contentPurchase?: {
    id: string
    contentType: 'NOVEL' | 'CHAPTER'
    contentId: string
  }
}

interface CreditTransactionHistoryProps {
  className?: string
  showFilters?: boolean
  compact?: boolean
  limit?: number
}

export function CreditTransactionHistory({
  className,
  showFilters = true,
  compact = false,
  limit
}: CreditTransactionHistoryProps) {
  const { data: session } = useSession()
  const { toast } = useToast()

  // Filter states
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(limit || 10)
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [dateRange, setDateRange] = useState<string>('all')

  // Calculate date range
  const getDateRange = () => {
    const now = new Date()
    switch (dateRange) {
      case 'week':
        return { startDate: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString() }
      case 'month':
        return { startDate: startOfMonth(now).toISOString() }
      case 'quarter':
        return { startDate: subMonths(now, 3).toISOString() }
      case 'year':
        return { startDate: subMonths(now, 12).toISOString() }
      default:
        return {}
    }
  }

  // RTK Query for transactions
  const {
    data: transactionsData,
    isLoading,
    refetch,
    isFetching
  } = useGetCreditTransactionsQuery({
    page: currentPage,
    limit: pageSize,
    type: typeFilter !== 'all' ? typeFilter : undefined,
    status: statusFilter !== 'all' ? statusFilter : undefined,
    search: searchTerm || undefined,
    ...getDateRange()
  }, {
    skip: !session?.user,
  })

  const transactions = transactionsData?.transactions || []
  const pagination = transactionsData?.pagination
  const [filter, setFilter] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [hasNext, setHasNext] = useState(false)
  const [hasPrev, setHasPrev] = useState(false)

  useEffect(() => {
    if (session) {
      loadTransactions()
    }
  }, [session, filter, currentPage])

  const loadTransactions = async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
      })
      
      if (filter !== 'all') {
        params.append('type', filter)
      }

      const response = await fetch(`/api/credits/transactions?${params}`)
      if (!response.ok) throw new Error('Failed to load transactions')
      
      const data = await response.json()
      setTransactions(data.transactions || [])
      setTotalPages(data.pagination?.totalPages || 1)
      setHasNext(data.pagination?.hasNext || false)
      setHasPrev(data.pagination?.hasPrev || false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load transaction history",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'PURCHASE':
        return <Plus className="h-4 w-4 text-green-500" />
      case 'SPEND':
        return <Minus className="h-4 w-4 text-red-500" />
      case 'REFUND':
        return <RefreshCw className="h-4 w-4 text-blue-500" />
      case 'BONUS':
        return <Gift className="h-4 w-4 text-purple-500" />
      case 'ADMIN_ADJUSTMENT':
        return <Settings className="h-4 w-4 text-gray-500" />
      default:
        return <History className="h-4 w-4 text-gray-500" />
    }
  }

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'PURCHASE':
      case 'REFUND':
      case 'BONUS':
        return 'text-green-600'
      case 'SPEND':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>
      case 'PENDING':
        return <Badge variant="secondary">Pending</Badge>
      case 'FAILED':
        return <Badge variant="destructive">Failed</Badge>
      case 'CANCELED':
        return <Badge variant="outline">Canceled</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  if (!session) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center text-muted-foreground">
            <History className="h-5 w-5 mr-2" />
            Sign in to view transaction history
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Transaction History
        </CardTitle>
        <CardDescription>
          View your credit purchase and spending history
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Filter */}
          <div className="flex items-center gap-4">
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter transactions" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Transactions</SelectItem>
                <SelectItem value="PURCHASE">Purchases</SelectItem>
                <SelectItem value="SPEND">Spending</SelectItem>
                <SelectItem value="REFUND">Refunds</SelectItem>
                <SelectItem value="BONUS">Bonuses</SelectItem>
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={loadTransactions}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>

          {/* Transactions List */}
          <div className="space-y-3">
            {isLoading ? (
              Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div>
                      <Skeleton className="h-4 w-32 mb-1" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
              ))
            ) : transactions.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No transactions found</p>
              </div>
            ) : (
              transactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gray-100 rounded-full">
                      {getTransactionIcon(transaction.type)}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{transaction.description}</p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{format(new Date(transaction.createdAt), 'MMM d, yyyy HH:mm')}</span>
                        {getStatusBadge(transaction.status)}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`font-bold ${getTransactionColor(transaction.type)}`}>
                      {transaction.amount > 0 ? '+' : ''}{formatCredits(transaction.amount}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Balance: {formatCredits(transaction.balanceAfter)}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(p => p - 1)}
                disabled={!hasPrev || isLoading}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
              
              <span className="text-sm text-muted-foreground">
                Page {currentPage} of {totalPages}
              </span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(p => p + 1)}
                disabled={!hasNext || isLoading}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
