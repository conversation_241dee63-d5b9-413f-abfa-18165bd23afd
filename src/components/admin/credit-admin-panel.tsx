"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { 
  Settings, 
  Plus, 
  Edit, 
  Trash2, 
  DollarSign, 
  Users,
  TrendingUp,
  AlertTriangle,
  RefreshCw
} from "lucide-react"
import { 
  useGetCreditPackagesQuery,
  useCreateCreditPackageMutation,
  useUpdateCreditPackageMutation,
  useDeleteCreditPackageMutation
} from "@/store/api/creditsApi"
import { formatCredits, formatCurrency } from "@/lib/credits"

interface CreditPackage {
  id: string
  name: string
  description: string
  credits: number
  bonusCredits: number
  price: number
  currency: string
  isActive: boolean
  sortOrder: number
  stripePriceId?: string
  createdAt: string
  updatedAt: string
}

export function CreditAdminPanel() {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [editingPackage, setEditingPackage] = useState<CreditPackage | null>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  // RTK Query hooks
  const { data: packagesData, isLoading, refetch } = useGetCreditPackagesQuery()
  const [createPackage, { isLoading: isCreating }] = useCreateCreditPackageMutation()
  const [updatePackage, { isLoading: isUpdating }] = useUpdateCreditPackageMutation()
  const [deletePackage, { isLoading: isDeleting }] = useDeleteCreditPackageMutation()

  const packages = packagesData?.packages || []

  // Check if user is admin
  if (!session?.user || session.user.role !== 'ADMIN') {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Access denied. Admin privileges required.</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const handleCreatePackage = async (formData: FormData) => {
    try {
      const packageData = {
        name: formData.get('name') as string,
        description: formData.get('description') as string,
        credits: parseInt(formData.get('credits') as string),
        bonusCredits: parseInt(formData.get('bonusCredits') as string) || 0,
        price: parseFloat(formData.get('price') as string),
        currency: 'usd',
        isActive: formData.get('isActive') === 'on',
        sortOrder: parseInt(formData.get('sortOrder') as string) || 0,
      }

      await createPackage(packageData).unwrap()
      
      toast({
        title: "Package Created",
        description: `Successfully created ${packageData.name}`,
      })
      
      setShowCreateDialog(false)
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create package",
        variant: "destructive",
      })
    }
  }

  const handleUpdatePackage = async (id: string, formData: FormData) => {
    try {
      const packageData = {
        name: formData.get('name') as string,
        description: formData.get('description') as string,
        credits: parseInt(formData.get('credits') as string),
        bonusCredits: parseInt(formData.get('bonusCredits') as string) || 0,
        price: parseFloat(formData.get('price') as string),
        isActive: formData.get('isActive') === 'on',
        sortOrder: parseInt(formData.get('sortOrder') as string) || 0,
      }

      await updatePackage({ id, data: packageData }).unwrap()
      
      toast({
        title: "Package Updated",
        description: `Successfully updated ${packageData.name}`,
      })
      
      setEditingPackage(null)
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update package",
        variant: "destructive",
      })
    }
  }

  const handleDeletePackage = async (id: string, name: string) => {
    if (!confirm(`Are you sure you want to delete "${name}"? This action cannot be undone.`)) {
      return
    }

    try {
      await deletePackage(id).unwrap()
      
      toast({
        title: "Package Deleted",
        description: `Successfully deleted ${name}`,
      })
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete package",
        variant: "destructive",
      })
    }
  }

  const PackageForm = ({ 
    package: pkg, 
    onSubmit, 
    isLoading: formLoading 
  }: { 
    package?: CreditPackage | null
    onSubmit: (formData: FormData) => void
    isLoading: boolean
  }) => (
    <form action={onSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="name">Package Name</Label>
          <Input
            id="name"
            name="name"
            defaultValue={pkg?.name || ''}
            placeholder="e.g., Starter Pack"
            required
          />
        </div>
        <div>
          <Label htmlFor="sortOrder">Sort Order</Label>
          <Input
            id="sortOrder"
            name="sortOrder"
            type="number"
            defaultValue={pkg?.sortOrder || 0}
            min="0"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          defaultValue={pkg?.description || ''}
          placeholder="Package description"
        />
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label htmlFor="credits">Credits</Label>
          <Input
            id="credits"
            name="credits"
            type="number"
            defaultValue={pkg?.credits || ''}
            min="1"
            required
          />
        </div>
        <div>
          <Label htmlFor="bonusCredits">Bonus Credits</Label>
          <Input
            id="bonusCredits"
            name="bonusCredits"
            type="number"
            defaultValue={pkg?.bonusCredits || 0}
            min="0"
          />
        </div>
        <div>
          <Label htmlFor="price">Price (USD)</Label>
          <Input
            id="price"
            name="price"
            type="number"
            step="0.01"
            defaultValue={pkg?.price || ''}
            min="0.01"
            required
          />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="isActive"
          name="isActive"
          defaultChecked={pkg?.isActive ?? true}
        />
        <Label htmlFor="isActive">Active</Label>
      </div>

      <DialogFooter>
        <Button type="submit" disabled={formLoading}>
          {formLoading ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              {pkg ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            pkg ? 'Update Package' : 'Create Package'
          )}
        </Button>
      </DialogFooter>
    </form>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Credit System Admin</h2>
          <p className="text-muted-foreground">
            Manage credit packages, monitor transactions, and view system analytics
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Package
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Credit Package</DialogTitle>
                <DialogDescription>
                  Create a new credit package for users to purchase
                </DialogDescription>
              </DialogHeader>
              <PackageForm
                onSubmit={handleCreatePackage}
                isLoading={isCreating}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="packages" className="w-full">
        <TabsList>
          <TabsTrigger value="packages">Packages</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>

        <TabsContent value="packages" className="space-y-4">
          <div className="grid gap-4">
            {packages.map((pkg) => (
              <Card key={pkg.id}>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div>
                        <h3 className="font-semibold">{pkg.name}</h3>
                        <p className="text-sm text-muted-foreground">{pkg.description}</p>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {formatCredits(pkg.credits)}
                          {pkg.bonusCredits > 0 && ` + ${formatCredits(pkg.bonusCredits)} bonus`}
                        </Badge>
                        <Badge variant="secondary">
                          ${pkg.price}
                        </Badge>
                        <Badge variant={pkg.isActive ? "default" : "secondary"}>
                          {pkg.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Edit Credit Package</DialogTitle>
                            <DialogDescription>
                              Update the credit package details
                            </DialogDescription>
                          </DialogHeader>
                          <PackageForm
                            package={pkg}
                            onSubmit={(formData) => handleUpdatePackage(pkg.id, formData)}
                            isLoading={isUpdating}
                          />
                        </DialogContent>
                      </Dialog>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeletePackage(pkg.id, pkg.name)}
                        disabled={isDeleting}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Packages</p>
                    <p className="text-2xl font-bold">{packages.length}</p>
                  </div>
                  <Settings className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Active Packages</p>
                    <p className="text-2xl font-bold">{packages.filter(p => p.isActive).length}</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Price Range</p>
                    <p className="text-2xl font-bold">
                      ${Math.min(...packages.map(p => p.price)} - ${Math.max(...packages.map(p => p.price)}
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Transaction Monitoring</CardTitle>
              <CardDescription>
                Real-time credit transaction monitoring and management
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Transaction monitoring coming soon</p>
                <p className="text-sm">Advanced transaction analytics and monitoring tools</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
