"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useDispatch } from "react-redux"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Coins, Plus, Info, AlertTriangle, Zap, TrendingUp } from "lucide-react"
import { formatCredits, creditsToUSD } from "@/lib/credits"
import { CreditPurchaseModal } from "@/components/credits/credit-purchase-modal"
import { useGetCreditBalanceQuery } from "@/store/api/creditsApi"
import { openPurchaseModal } from "@/store/slices/creditSlice"
import type { AppDispatch } from "@/lib/store"

interface CreditBalanceWidgetProps {
  className?: string
  showPurchaseButton?: boolean
  compact?: boolean
  showLowBalanceAlert?: boolean
}

export function CreditBalanceWidget({
  className,
  showPurchaseButton = true,
  compact = false,
  showLowBalanceAlert = true
}: CreditBalanceWidgetProps) {
  const { data: session } = useSession()
  const dispatch = useDispatch<AppDispatch>()
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)

  // Get current credit balance
  const {
    data: balanceData,
    isLoading: isLoadingBalance,
    refetch: refetchBalance
  } = useGetCreditBalanceQuery(undefined, {
    skip: !session?.user,
    pollingInterval: 30000, // Poll every 30 seconds
  })

  const currentBalance = balanceData?.balance ?? 0

  const handleTopUpClick = () => {
    console.log('Top up clicked! Session:', session?.user)
    console.log('Dispatching openPurchaseModal...')
    dispatch(openPurchaseModal(null))
  }

  // If user is not authenticated, show sign-in prompt
  if (!session?.user) {
    if (compact) return null
    
    return (
      <Card className={`border-blue-200 bg-blue-50 ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Info className="h-5 w-5" />
            Sign In Required
          </CardTitle>
          <CardDescription className="text-blue-600">
            Sign in to view your credit balance
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  if (compact) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Coins className="h-4 w-4 text-yellow-500" />
        <span className="text-sm font-medium">Credits:</span>
        {isLoadingBalance ? (
          <Skeleton className="h-5 w-12" />
        ) : (
          <Badge
            variant={currentBalance < 10 ? "destructive" : currentBalance < 50 ? "secondary" : "default"}
            className="font-bold cursor-pointer hover:opacity-80 transition-opacity"
            onClick={handleTopUpClick}
            title="Click to top up credits"
          >
            {formatCredits(currentBalance)}
          </Badge>
        )}
        {showPurchaseButton && (
          <div className="flex items-center gap-1">
            {currentBalance < 20 && (
              <Button
                onClick={handleTopUpClick}
                size="sm"
                variant="default"
                className="h-6 px-2 text-xs"
              >
                <Zap className="h-3 w-3 mr-1" />
                Top Up
              </Button>
            )}
            <Button
              onClick={handleTopUpClick}
              size="sm"
              variant="outline"
              className="h-6 px-2 text-xs"
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>
        )}
      </div>
    )
  }

  return (
    <>
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5 text-yellow-500" />
            Your Credits
          </CardTitle>
          <CardDescription>
            Use credits to unlock premium chapters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Balance */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Current Balance:</span>
            {isLoadingBalance ? (
              <Skeleton className="h-6 w-16" />
            ) : (
              <div className="flex items-center gap-2">
                <Badge
                  variant={currentBalance < 10 ? "destructive" : currentBalance < 50 ? "secondary" : "default"}
                  className="text-lg font-bold px-3 py-1"
                >
                  {formatCredits(currentBalance)}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  (${creditsToUSD(currentBalance).toFixed(2)})
                </span>
              </div>
            )}
          </div>

          {/* Low Balance Alert */}
          {showLowBalanceAlert && currentBalance < 20 && (
            <Alert className="border-orange-200 bg-orange-50">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                <div className="font-medium mb-1">Low Credit Balance</div>
                <div className="text-sm">
                  Consider topping up to continue enjoying premium content without interruption.
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Top Up Button */}
          {showPurchaseButton && (
            <div className="pt-2 border-t space-y-2">
              {currentBalance < 50 && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <TrendingUp className="h-4 w-4" />
                  <span>Value packs available with bonus credits</span>
                </div>
              )}
              <Button
                onClick={handleTopUpClick}
                variant="outline"
                size="sm"
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Buy More Credits
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Credit Purchase Modal */}
      <CreditPurchaseModal />
    </>
  )
}
