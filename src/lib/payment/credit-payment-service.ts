import { stripe } from "@/lib/stripe"
import { prisma } from "@/lib/db"
// Note: Using string literals instead of enums for SQLite compatibility
import { CreditPurchaseNotificationService } from "@/lib/notifications/credit-purchase-notifications"
import { CreditTransactionManager } from "@/lib/database/credit-transaction-manager"
import <PERSON><PERSON> from "stripe"

export interface PaymentMethodInfo {
  id: string
  type: 'card' | 'apple_pay' | 'google_pay' | 'link'
  card?: {
    brand: string
    last4: string
    exp_month: number
    exp_year: number
  }
}

export interface CreditPurchaseRequest {
  packageId: string
  paymentMethodId?: string
  savePaymentMethod?: boolean
  useStoredPaymentMethod?: boolean
  storedPaymentMethodId?: string
}

export interface CreditPurchaseResult {
  purchase: any
  clientSecret?: string
  requiresAction: boolean
  status: 'succeeded' | 'requires_action' | 'requires_payment_method' | 'failed'
  error?: string
}

export class CreditPaymentService {
  /**
   * Create or retrieve Stripe customer for user
   */
  static async getOrCreateStripeCustomer(userId: string, email: string): Promise<string> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { stripeCustomerId: true }
    })

    if (user?.stripeCustomerId) {
      return user.stripeCustomerId
    }

    // Create new Stripe customer
    const customer = await stripe.customers.create({
      email,
      metadata: {
        userId,
      },
    })

    // Update user with Stripe customer ID
    await prisma.user.update({
      where: { id: userId },
      data: { stripeCustomerId: customer.id }
    })

    return customer.id
  }

  /**
   * Get user's saved payment methods
   */
  static async getUserPaymentMethods(stripeCustomerId: string): Promise<PaymentMethodInfo[]> {
    try {
      const paymentMethods = await stripe.paymentMethods.list({
        customer: stripeCustomerId,
        type: 'card',
      })

      return paymentMethods.data.map(pm => ({
        id: pm.id,
        type: pm.type as any,
        card: pm.card ? {
          brand: pm.card.brand,
          last4: pm.card.last4,
          exp_month: pm.card.exp_month,
          exp_year: pm.card.exp_year,
        } : undefined
      }))
    } catch (error) {
      console.error('Error fetching payment methods:', error)
      return []
    }
  }

  /**
   * Create payment intent with enhanced error handling
   */
  static async createPaymentIntent(
    request: CreditPurchaseRequest,
    userId: string,
    stripeCustomerId: string
  ): Promise<CreditPurchaseResult> {
    try {
      // Get credit package
      const creditPackage = await prisma.creditPackage.findUnique({
        where: { id: request.packageId }
      })

      if (!creditPackage || !creditPackage.isActive) {
        return {
          purchase: null,
          requiresAction: false,
          status: 'failed',
          error: 'Credit package not found or inactive'
        }
      }

      // Determine payment method
      let paymentMethodId = request.paymentMethodId
      if (request.useStoredPaymentMethod && request.storedPaymentMethodId) {
        paymentMethodId = request.storedPaymentMethodId
      }

      if (!paymentMethodId) {
        return {
          purchase: null,
          requiresAction: false,
          status: 'requires_payment_method',
          error: 'Payment method required'
        }
      }

      // Create payment intent
      const paymentIntentData: Stripe.PaymentIntentCreateParams = {
        amount: Math.round(creditPackage.price * 100),
        currency: creditPackage.currency,
        customer: stripeCustomerId,
        payment_method: paymentMethodId,
        confirmation_method: 'manual',
        confirm: true,
        return_url: `${process.env.NEXTAUTH_URL}/dashboard/credits`,
        metadata: {
          type: 'credit_purchase',
          packageId: creditPackage.id,
          userId,
          credits: creditPackage.credits.toString(),
          bonusCredits: creditPackage.bonusCredits.toString(),
        },
      }

      // Save payment method if requested
      if (request.savePaymentMethod && !request.useStoredPaymentMethod) {
        paymentIntentData.setup_future_usage = 'off_session'
      }

      const paymentIntent = await stripe.paymentIntents.create(paymentIntentData)

      // Use transaction manager for safe credit purchase processing
      const totalCredits = creditPackage.credits + creditPackage.bonusCredits

      const { purchase: creditPurchase, newBalance } = await CreditTransactionManager.executeCreditPurchase({
        userId,
        packageId: creditPackage.id,
        credits: creditPackage.credits,
        bonusCredits: creditPackage.bonusCredits,
        totalCredits,
        amount: creditPackage.price,
        currency: creditPackage.currency,
        stripePaymentId: paymentIntent.id,
        status: this.mapStripeStatusToPaymentStatus(paymentIntent.status),
      })

      // Send notification for successful purchases
      if (paymentIntent.status === 'succeeded') {
        try {
          await CreditPurchaseNotificationService.sendPurchaseConfirmation({
            purchaseId: creditPurchase.id,
            userEmail: creditPurchase.user.email,
            userName: creditPurchase.user.name || 'Valued Customer',
            packageName: creditPurchase.package.name,
            credits: creditPurchase.credits,
            bonusCredits: creditPurchase.bonusCredits,
            totalCredits: creditPurchase.totalCredits,
            amount: creditPurchase.amount,
            currency: creditPurchase.currency,
            purchaseDate: creditPurchase.createdAt,
            stripePaymentId: creditPurchase.stripePaymentId || undefined
          })
        } catch (emailError) {
          console.error('Failed to send purchase confirmation email:', emailError)
          // Don't throw - email failure shouldn't fail the purchase
        }
      }

      return {
        purchase: creditPurchase,
        clientSecret: paymentIntent.client_secret,
        requiresAction: paymentIntent.status === 'requires_action',
        status: paymentIntent.status as any
      }

    } catch (error: any) {
      console.error('Payment intent creation error:', error)
      
      // Handle specific Stripe errors
      if (error.type === 'StripeCardError') {
        return {
          purchase: null,
          requiresAction: false,
          status: 'failed',
          error: error.message || 'Your card was declined'
        }
      }

      if (error.type === 'StripeInvalidRequestError') {
        return {
          purchase: null,
          requiresAction: false,
          status: 'failed',
          error: 'Invalid payment information'
        }
      }

      return {
        purchase: null,
        requiresAction: false,
        status: 'failed',
        error: 'Payment processing failed. Please try again.'
      }
    }
  }

  /**
   * Process successful credit purchase (legacy method - now handled by transaction manager)
   * @deprecated Use CreditTransactionManager.executeCreditPurchase instead
   */
  static async processSuccessfulCreditPurchase(purchaseId: string): Promise<void> {
    console.warn('processSuccessfulCreditPurchase is deprecated. Use CreditTransactionManager.executeCreditPurchase instead.')

    const purchase = await prisma.creditPurchase.findUnique({
      where: { id: purchaseId },
      include: { user: true, package: true }
    })

    if (!purchase) {
      throw new Error('Purchase not found')
    }

    // Use the new transaction manager for safety
    await CreditTransactionManager.executeCreditRefund(
      purchase.userId,
      purchase.totalCredits,
      `Credit purchase: ${purchase.package.name}`,
      'purchase',
      purchase.id,
      {
        packageName: purchase.package.name,
        baseCredits: purchase.credits,
        bonusCredits: purchase.bonusCredits,
        stripePaymentId: purchase.stripePaymentId
      }
    )

    // Send purchase confirmation email
    try {
      await CreditPurchaseNotificationService.sendPurchaseConfirmation({
        purchaseId: purchase.id,
        userEmail: purchase.user.email,
        userName: purchase.user.name || 'Valued Customer',
        packageName: purchase.package.name,
        credits: purchase.credits,
        bonusCredits: purchase.bonusCredits,
        totalCredits: purchase.totalCredits,
        amount: purchase.amount,
        currency: purchase.currency,
        purchaseDate: purchase.createdAt,
        stripePaymentId: purchase.stripePaymentId || undefined
      })
    } catch (emailError) {
      console.error('Failed to send purchase confirmation email:', emailError)
      // Don't throw - email failure shouldn't fail the purchase
    }
  }

  /**
   * Map Stripe payment status to our PaymentStatus string
   */
  private static mapStripeStatusToPaymentStatus(stripeStatus: string): string {
    switch (stripeStatus) {
      case 'succeeded':
        return "COMPLETED"
      case 'requires_action':
      case 'requires_confirmation':
      case 'requires_payment_method':
        return "PENDING"
      case 'canceled':
        return "CANCELED"
      case 'processing':
        return "PENDING"
      default:
        return "FAILED"
    }
  }

  /**
   * Handle payment method attachment for future use
   */
  static async attachPaymentMethodToCustomer(
    paymentMethodId: string,
    customerId: string
  ): Promise<void> {
    try {
      await stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      })
    } catch (error) {
      console.error('Error attaching payment method:', error)
      // Don't throw - this is not critical for the purchase
    }
  }

  /**
   * Detach payment method from customer
   */
  static async detachPaymentMethod(paymentMethodId: string): Promise<void> {
    try {
      await stripe.paymentMethods.detach(paymentMethodId)
    } catch (error) {
      console.error('Error detaching payment method:', error)
      throw new Error('Failed to remove payment method')
    }
  }
}
