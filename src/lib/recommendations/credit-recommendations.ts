import { prisma } from "@/lib/db"

export interface CreditPackage {
  id: string
  name: string
  description: string
  credits: number
  bonusCredits: number
  price: number
  currency: string
  sortOrder: number
  isActive: boolean
  isPopular: boolean
  isBestValue: boolean
}

export interface UserReadingHabits {
  averageCreditsPerWeek: number
  averageCreditsPerMonth: number
  totalCreditsSpent: number
  totalPurchases: number
  preferredGenres: string[]
  readingFrequency: 'light' | 'moderate' | 'heavy'
  lastPurchaseDate?: Date
  averagePurchaseAmount: number
}

export interface CreditRecommendation {
  packageId: string
  package: CreditPackage
  reason: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  savings?: number
  matchScore: number
  benefits: string[]
  context: 'insufficient_credits' | 'low_balance' | 'value_optimization' | 'usage_pattern'
}

export class CreditRecommendationEngine {
  /**
   * Get personalized credit package recommendations for a user
   */
  static async getRecommendations(
    userId: string,
    currentBalance: number,
    requiredCredits?: number,
    context?: string
  ): Promise<CreditRecommendation[]> {
    try {
      // Get available packages
      const packages = await prisma.creditPackage.findMany({
        where: { isActive: true },
        orderBy: { sortOrder: 'asc' }
      })

      if (packages.length === 0) {
        return []
      }

      // Get user reading habits
      const readingHabits = await this.analyzeUserReadingHabits(userId)
      
      // Generate recommendations based on different scenarios
      const recommendations: CreditRecommendation[] = []

      // Scenario 1: Insufficient credits for specific content
      if (requiredCredits && currentBalance < requiredCredits) {
        recommendations.push(...this.getInsufficientCreditsRecommendations(
          packages, currentBalance, requiredCredits, readingHabits
        ))
      }

      // Scenario 2: Low balance warning
      if (currentBalance < 20) {
        recommendations.push(...this.getLowBalanceRecommendations(
          packages, currentBalance, readingHabits
        ))
      }

      // Scenario 3: Value optimization recommendations
      if (currentBalance >= 20 && currentBalance < 100) {
        recommendations.push(...this.getValueOptimizationRecommendations(
          packages, currentBalance, readingHabits
        ))
      }

      // Scenario 4: Usage pattern recommendations
      recommendations.push(...this.getUsagePatternRecommendations(
        packages, currentBalance, readingHabits
      ))

      // Sort by priority and match score
      return recommendations
        .sort((a, b) => {
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 }
          const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority]
          if (priorityDiff !== 0) return priorityDiff
          return b.matchScore - a.matchScore
        })
        .slice(0, 3) // Return top 3 recommendations

    } catch (error) {
      console.error('Error generating credit recommendations:', error)
      return []
    }
  }

  /**
   * Analyze user's reading habits and spending patterns
   */
  private static async analyzeUserReadingHabits(userId: string): Promise<UserReadingHabits> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)

    // Get credit spending transactions
    const spendingTransactions = await prisma.creditTransaction.findMany({
      where: {
        userId,
        type: 'DEBIT',
        status: 'COMPLETED',
        createdAt: { gte: thirtyDaysAgo }
      },
      orderBy: { createdAt: 'desc' }
    })

    // Get purchase history
    const purchases = await prisma.creditPurchase.findMany({
      where: {
        userId,
        status: 'COMPLETED'
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    })

    // Calculate metrics
    const weeklySpending = spendingTransactions
      .filter(t => t.createdAt >= sevenDaysAgo)
      .reduce((sum, t) => sum + Math.abs(t.amount, 0)

    const monthlySpending = spendingTransactions
      .reduce((sum, t) => sum + Math.abs(t.amount, 0)

    const totalCreditsSpent = spendingTransactions
      .reduce((sum, t) => sum + Math.abs(t.amount, 0)

    const averagePurchaseAmount = purchases.length > 0
      ? purchases.reduce((sum, p) => sum + p.amount, 0) / purchases.length
      : 0

    // Determine reading frequency
    let readingFrequency: 'light' | 'moderate' | 'heavy' = 'light'
    if (weeklySpending > 50) readingFrequency = 'heavy'
    else if (weeklySpending > 20) readingFrequency = 'moderate'

    return {
      averageCreditsPerWeek: weeklySpending,
      averageCreditsPerMonth: monthlySpending,
      totalCreditsSpent,
      totalPurchases: purchases.length,
      preferredGenres: [], // Would need additional data to determine
      readingFrequency,
      lastPurchaseDate: purchases[0]?.createdAt,
      averagePurchaseAmount
    }
  }

  /**
   * Get recommendations for insufficient credits scenario
   */
  private static getInsufficientCreditsRecommendations(
    packages: CreditPackage[],
    currentBalance: number,
    requiredCredits: number,
    habits: UserReadingHabits
  ): CreditRecommendation[] {
    const shortfall = requiredCredits - currentBalance
    const recommendations: CreditRecommendation[] = []

    // Find packages that cover the shortfall
    const suitablePackages = packages.filter(pkg => 
      (pkg.credits + pkg.bonusCredits) >= shortfall
    )

    if (suitablePackages.length === 0) return recommendations

    // Recommend the most cost-effective package that covers the shortfall
    const exactMatch = suitablePackages
      .sort((a, b) => {
        const aTotal = a.credits + a.bonusCredits
        const bTotal = b.credits + b.bonusCredits
        const aDiff = Math.abs(aTotal - shortfall)
        const bDiff = Math.abs(bTotal - shortfall)
        return aDiff - bDiff
      })[0]

    if (exactMatch) {
      const totalCredits = exactMatch.credits + exactMatch.bonusCredits
      const leftover = totalCredits - shortfall
      
      recommendations.push({
        packageId: exactMatch.id,
        package: exactMatch,
        reason: `Perfect for unlocking this content with ${leftover > 0 ? `${leftover} credits left over` : 'exact amount needed'}`,
        priority: 'urgent',
        matchScore: 95,
        benefits: [
          `Unlocks the content you want`,
          leftover > 0 ? `${leftover} credits for future reading` : 'Exact amount needed',
          exactMatch.bonusCredits > 0 ? `Includes ${exactMatch.bonusCredits} bonus credits` : ''
        ].filter(Boolean),
        context: 'insufficient_credits'
      })
    }

    return recommendations
  }

  /**
   * Get recommendations for low balance scenario
   */
  private static getLowBalanceRecommendations(
    packages: CreditPackage[],
    currentBalance: number,
    habits: UserReadingHabits
  ): CreditRecommendation[] {
    const recommendations: CreditRecommendation[] = []

    // For light readers, recommend starter packages
    if (habits.readingFrequency === 'light') {
      const starterPackage = packages.find(pkg => pkg.credits <= 100) || packages[0]
      if (starterPackage) {
        recommendations.push({
          packageId: starterPackage.id,
          package: starterPackage,
          reason: 'Perfect starter pack for casual reading',
          priority: 'high',
          matchScore: 85,
          benefits: [
            'Great for trying premium content',
            'Affordable entry point',
            starterPackage.bonusCredits > 0 ? `Includes ${starterPackage.bonusCredits} bonus credits` : ''
          ].filter(Boolean),
          context: 'low_balance'
        })
      }
    }

    // For moderate/heavy readers, recommend value packages
    if (habits.readingFrequency !== 'light') {
      const valuePackage = packages.find(pkg => pkg.bonusCredits > 0) || packages[1]
      if (valuePackage) {
        const bonusPercentage = Math.round((valuePackage.bonusCredits / valuePackage.credits) * 100)
        recommendations.push({
          packageId: valuePackage.id,
          package: valuePackage,
          reason: `Best value with ${bonusPercentage}% bonus credits`,
          priority: 'high',
          matchScore: 90,
          savings: valuePackage.bonusCredits * 0.1, // Assuming $0.10 per credit
          benefits: [
            `${bonusPercentage}% more credits for free`,
            'Perfect for regular readers',
            'Better value per credit'
          ],
          context: 'low_balance'
        })
      }
    }

    return recommendations
  }

  /**
   * Get value optimization recommendations
   */
  private static getValueOptimizationRecommendations(
    packages: CreditPackage[],
    currentBalance: number,
    habits: UserReadingHabits
  ): CreditRecommendation[] {
    const recommendations: CreditRecommendation[] = []

    // Find packages with best bonus percentages
    const bonusPackages = packages
      .filter(pkg => pkg.bonusCredits > 0)
      .sort((a, b) => {
        const aBonus = (a.bonusCredits / a.credits) * 100
        const bBonus = (b.bonusCredits / b.credits) * 100
        return bBonus - aBonus
      })

    if (bonusPackages.length > 0) {
      const bestValue = bonusPackages[0]
      const bonusPercentage = Math.round((bestValue.bonusCredits / bestValue.credits) * 100)
      
      recommendations.push({
        packageId: bestValue.id,
        package: bestValue,
        reason: `Maximize your credits with ${bonusPercentage}% bonus`,
        priority: 'medium',
        matchScore: 80,
        savings: bestValue.bonusCredits * 0.1,
        benefits: [
          `${bonusPercentage}% bonus credits`,
          'Best value for money',
          'Stock up and save'
        ],
        context: 'value_optimization'
      })
    }

    return recommendations
  }

  /**
   * Get recommendations based on usage patterns
   */
  private static getUsagePatternRecommendations(
    packages: CreditPackage[],
    currentBalance: number,
    habits: UserReadingHabits
  ): CreditRecommendation[] {
    const recommendations: CreditRecommendation[] = []

    // Based on weekly usage, recommend appropriate packages
    if (habits.averageCreditsPerWeek > 0) {
      const weeklyNeed = habits.averageCreditsPerWeek
      const monthlyNeed = weeklyNeed * 4

      // Find package that covers monthly needs
      const monthlyPackage = packages.find(pkg => 
        (pkg.credits + pkg.bonusCredits) >= monthlyNeed
      )

      if (monthlyPackage && monthlyNeed > currentBalance) {
        recommendations.push({
          packageId: monthlyPackage.id,
          package: monthlyPackage,
          reason: `Covers your monthly reading needs (${Math.round(monthlyNeed)} credits)`,
          priority: 'medium',
          matchScore: 75,
          benefits: [
            'Based on your reading habits',
            'Covers a full month of reading',
            monthlyPackage.bonusCredits > 0 ? 'Includes bonus credits' : ''
          ].filter(Boolean),
          context: 'usage_pattern'
        })
      }
    }

    return recommendations
  }

  /**
   * Get contextual recommendations for specific scenarios
   */
  static async getContextualRecommendations(
    userId: string,
    context: 'chapter_access' | 'novel_unlock' | 'subscription_renewal',
    metadata?: any
  ): Promise<CreditRecommendation[]> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { creditBalance: true }
    })

    if (!user) return []

    switch (context) {
      case 'chapter_access':
        return this.getRecommendations(
          userId, 
          user.creditBalance, 
          metadata?.requiredCredits,
          'chapter_access'
        )
      
      case 'novel_unlock':
        return this.getRecommendations(
          userId,
          user.creditBalance,
          metadata?.totalChapterCredits,
          'novel_unlock'
        )
      
      default:
        return this.getRecommendations(userId, user.creditBalance)
    }
  }
}
