import { prisma } from "@/lib/db"
import { formatCredits, creditsToUSD } from "@/lib/credits"
import { EmailService } from "@/lib/email/email-service"

interface CreditPurchaseEmailData {
  purchaseId: string
  userEmail: string
  userName: string
  packageName: string
  credits: number
  bonusCredits: number
  totalCredits: number
  amount: number
  currency: string
  purchaseDate: Date
  stripePaymentId?: string
}

interface LowBalanceEmailData {
  userEmail: string
  userName: string
  currentBalance: number
  recommendedPackage?: {
    name: string
    credits: number
    price: number
  }
}

export class CreditPurchaseNotificationService {
  /**
   * Send purchase confirmation email
   */
  static async sendPurchaseConfirmation(data: CreditPurchaseEmailData): Promise<void> {
    try {
      const emailContent = this.generatePurchaseConfirmationEmail(data)

      // Send email using the email service
      const result = await EmailService.sendEmail({
        to: data.userEmail,
        subject: `Credit Purchase Confirmation - ${formatCredits(data.totalCredits)}`,
        html: emailContent,
        text: this.generatePurchaseConfirmationText(data)
      })

      // Store email notification record
      await prisma.emailNotification.create({
        data: {
          type: 'CREDIT_PURCHASE_CONFIRMATION',
          recipient: data.userEmail,
          subject: `Credit Purchase Confirmation - ${formatCredits(data.totalCredits)}`,
          content: emailContent,
          status: result.success ? 'SENT' : 'FAILED',
          messageId: result.messageId,
          metadata: {
            purchaseId: data.purchaseId,
            credits: data.totalCredits,
            amount: data.amount
          }
        }
      }).catch(error => {
        // Handle case where EmailNotification model doesn't exist yet
        console.log('Email notification logged (no DB model):', error.message)
      })

    } catch (error) {
      console.error('Error sending purchase confirmation email:', error)
      throw error
    }
  }

  /**
   * Send low balance warning email
   */
  static async sendLowBalanceWarning(data: LowBalanceEmailData): Promise<void> {
    try {
      const emailContent = this.generateLowBalanceWarningEmail(data)

      // Send email using the email service
      const result = await EmailService.sendEmail({
        to: data.userEmail,
        subject: 'Your Credit Balance is Running Low',
        html: emailContent,
        text: this.generateLowBalanceWarningText(data)
      })

      // Store email notification record
      await prisma.emailNotification.create({
        data: {
          type: 'LOW_BALANCE_WARNING',
          recipient: data.userEmail,
          subject: 'Your Credit Balance is Running Low',
          content: emailContent,
          status: result.success ? 'SENT' : 'FAILED',
          messageId: result.messageId,
          metadata: {
            currentBalance: data.currentBalance,
            recommendedPackage: data.recommendedPackage?.name
          }
        }
      }).catch(error => {
        console.log('Email notification logged (no DB model):', error.message)
      })

    } catch (error) {
      console.error('Error sending low balance warning email:', error)
      throw error
    }
  }

  /**
   * Send purchase receipt email
   */
  static async sendPurchaseReceipt(purchaseId: string): Promise<void> {
    try {
      const purchase = await prisma.creditPurchase.findUnique({
        where: { id: purchaseId },
        include: {
          user: true,
          package: true
        }
      })

      if (!purchase || !purchase.user) {
        throw new Error('Purchase or user not found')
      }

      const emailContent = this.generatePurchaseReceiptEmail({
        purchaseId: purchase.id,
        userEmail: purchase.user.email,
        userName: purchase.user.name || 'Valued Customer',
        packageName: purchase.package.name,
        credits: purchase.credits,
        bonusCredits: purchase.bonusCredits,
        totalCredits: purchase.totalCredits,
        amount: purchase.amount,
        currency: purchase.currency,
        purchaseDate: purchase.createdAt,
        stripePaymentId: purchase.stripePaymentId || undefined
      })

      console.log('Purchase Receipt Email:', {
        to: purchase.user.email,
        subject: `Receipt for Credit Purchase #${purchase.id.slice(-8)}`,
        content: emailContent
      })

    } catch (error) {
      console.error('Error sending purchase receipt email:', error)
      throw error
    }
  }

  /**
   * Generate purchase confirmation email text
   */
  private static generatePurchaseConfirmationText(data: CreditPurchaseEmailData): string {
    return `
Credit Purchase Confirmation

Hi ${data.userName}!

Thank you for your credit purchase! Your credits have been added to your account and are ready to use.

Purchase Details:
- Package: ${data.packageName}
- Credits: ${formatCredits(data.credits)}
${data.bonusCredits > 0 ? `- Bonus Credits: +${formatCredits(data.bonusCredits)}` : ''}
- Total Credits: ${formatCredits(data.totalCredits)}
- Amount Paid: $${data.amount.toFixed(2)} ${data.currency.toUpperCase()}
- Purchase Date: ${data.purchaseDate.toLocaleDateString()}
- Transaction ID: ${data.purchaseId}

Your credits are now available in your account. Start exploring premium content and support your favorite authors!

View My Credits: ${process.env.NEXTAUTH_URL}/dashboard/credits

Questions? Contact <NAME_EMAIL>

© ${new Date().getFullYear()} Black Blogs. All rights reserved.
    `.trim()
  }

  /**
   * Generate low balance warning email text
   */
  private static generateLowBalanceWarningText(data: LowBalanceEmailData): string {
    return `
Low Credit Balance Warning

Hi ${data.userName}!

Your credit balance is running low!

Current Balance: ${formatCredits(data.currentBalance)}

Don't let a low balance interrupt your reading experience. Top up your credits now to continue enjoying premium content.

${data.recommendedPackage ? `
Recommended for You:
${data.recommendedPackage.name} - ${formatCredits(data.recommendedPackage.credits)} for $${data.recommendedPackage.price}
Perfect for continuing your reading journey!
` : ''}

Top Up Credits: ${process.env.NEXTAUTH_URL}/dashboard/credits

You can manage your notification preferences in your account settings.

© ${new Date().getFullYear()} Black Blogs. All rights reserved.
    `.trim()
  }

  /**
   * Generate purchase confirmation email HTML
   */
  private static generatePurchaseConfirmationEmail(data: CreditPurchaseEmailData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Credit Purchase Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #4F46E5; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .highlight { background: #EEF2FF; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .credits { font-size: 24px; font-weight: bold; color: #4F46E5; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Credit Purchase Confirmed!</h1>
          </div>
          
          <div class="content">
            <h2>Hi ${data.userName}!</h2>
            
            <p>Thank you for your credit purchase! Your credits have been added to your account and are ready to use.</p>
            
            <div class="highlight">
              <h3>Purchase Details</h3>
              <p><strong>Package:</strong> ${data.packageName}</p>
              <p><strong>Credits:</strong> <span class="credits">${formatCredits(data.credits)}</span></p>
              ${data.bonusCredits > 0 ? `<p><strong>Bonus Credits:</strong> <span class="credits">+${formatCredits(data.bonusCredits)}</span></p>` : ''}
              <p><strong>Total Credits:</strong> <span class="credits">${formatCredits(data.totalCredits)}</span></p>
              <p><strong>Amount Paid:</strong> $${data.amount.toFixed(2)} ${data.currency.toUpperCase()}</p>
              <p><strong>Purchase Date:</strong> ${data.purchaseDate.toLocaleDateString()}</p>
              <p><strong>Transaction ID:</strong> ${data.purchaseId}</p>
            </div>
            
            <p>Your credits are now available in your account. Start exploring premium content and support your favorite authors!</p>
            
            <p style="text-align: center;">
              <a href="${process.env.NEXTAUTH_URL}/dashboard/credits" 
                 style="background: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                View My Credits
              </a>
            </p>
          </div>
          
          <div class="footer">
            <p>Questions? Contact <NAME_EMAIL></p>
            <p>© ${new Date().getFullYear()} Black Blogs. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  /**
   * Generate low balance warning email HTML
   */
  private static generateLowBalanceWarningEmail(data: LowBalanceEmailData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Low Credit Balance Warning</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #F59E0B; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .warning { background: #FEF3C7; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #F59E0B; }
          .recommendation { background: #EEF2FF; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>⚠️ Low Credit Balance</h1>
          </div>
          
          <div class="content">
            <h2>Hi ${data.userName}!</h2>
            
            <div class="warning">
              <p><strong>Your credit balance is running low!</strong></p>
              <p>Current Balance: <strong>${formatCredits(data.currentBalance)}</strong></p>
            </div>
            
            <p>Don't let a low balance interrupt your reading experience. Top up your credits now to continue enjoying premium content.</p>
            
            ${data.recommendedPackage ? `
              <div class="recommendation">
                <h3>💡 Recommended for You</h3>
                <p><strong>${data.recommendedPackage.name}</strong></p>
                <p>${formatCredits(data.recommendedPackage.credits)} for $${data.recommendedPackage.price}</p>
                <p>Perfect for continuing your reading journey!</p>
              </div>
            ` : ''}
            
            <p style="text-align: center;">
              <a href="${process.env.NEXTAUTH_URL}/dashboard/credits" 
                 style="background: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Top Up Credits
              </a>
            </p>
          </div>
          
          <div class="footer">
            <p>You can manage your notification preferences in your account settings.</p>
            <p>© ${new Date().getFullYear()} Black Blogs. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  /**
   * Generate purchase receipt email HTML
   */
  private static generatePurchaseReceiptEmail(data: CreditPurchaseEmailData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Purchase Receipt</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #1F2937; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .receipt { background: white; padding: 20px; border-radius: 8px; margin: 15px 0; }
          .receipt-header { border-bottom: 2px solid #E5E7EB; padding-bottom: 15px; margin-bottom: 15px; }
          .receipt-row { display: flex; justify-content: space-between; margin: 10px 0; }
          .total-row { border-top: 2px solid #E5E7EB; padding-top: 15px; margin-top: 15px; font-weight: bold; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📧 Purchase Receipt</h1>
          </div>
          
          <div class="content">
            <div class="receipt">
              <div class="receipt-header">
                <h2>Black Blogs</h2>
                <p>Receipt #${data.purchaseId.slice(-8)}</p>
                <p>Date: ${data.purchaseDate.toLocaleDateString()}</p>
              </div>
              
              <h3>Bill To:</h3>
              <p>${data.userName}<br>${data.userEmail}</p>
              
              <h3>Items:</h3>
              <div class="receipt-row">
                <span>${data.packageName}</span>
                <span>$${data.amount.toFixed(2)}</span>
              </div>
              <div class="receipt-row">
                <span style="margin-left: 20px; color: #666;">Credits: ${formatCredits(data.credits)}</span>
                <span></span>
              </div>
              ${data.bonusCredits > 0 ? `
                <div class="receipt-row">
                  <span style="margin-left: 20px; color: #10B981;">Bonus Credits: ${formatCredits(data.bonusCredits)}</span>
                  <span style="color: #10B981;">FREE</span>
                </div>
              ` : ''}
              
              <div class="receipt-row total-row">
                <span>Total</span>
                <span>$${data.amount.toFixed(2)} ${data.currency.toUpperCase()}</span>
              </div>
              
              <div class="receipt-row">
                <span>Total Credits Received</span>
                <span>${formatCredits(data.totalCredits)}</span>
              </div>
              
              ${data.stripePaymentId ? `
                <p style="margin-top: 20px; font-size: 12px; color: #666;">
                  Payment ID: ${data.stripePaymentId}
                </p>
              ` : ''}
            </div>
            
            <p>Thank you for your purchase! Your credits are now available in your account.</p>
          </div>
          
          <div class="footer">
            <p>Keep this receipt for your records.</p>
            <p>© ${new Date().getFullYear()} Black Blogs. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `
  }
}
