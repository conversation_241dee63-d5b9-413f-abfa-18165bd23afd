import { prisma } from "@/lib/db"
import { CreditPurchaseNotificationService } from "./credit-purchase-notifications"
import { CreditRecommendationEngine } from "@/lib/recommendations/credit-recommendations"

export interface ScheduledEmailJob {
  id: string
  type: 'LOW_BALANCE_WARNING' | 'PURCHASE_REMINDER' | 'WEEKLY_DIGEST' | 'MONTHLY_SUMMARY'
  userId: string
  scheduledFor: Date
  data: Record<string, any>
  status: 'PENDING' | 'SENT' | 'FAILED' | 'CANCELLED'
}

export class EmailScheduler {
  /**
   * Schedule low balance warning emails for users
   */
  static async scheduleWeeklyLowBalanceCheck(): Promise<void> {
    try {
      console.log('🔍 Running weekly low balance check...')

      // Find users with low balance who haven't received a warning recently
      const lowBalanceUsers = await prisma.user.findMany({
        where: {
          creditBalance: { lt: 20 }, // Less than 20 credits
          emailNotifications: true,
          OR: [
            { lastLowBalanceWarning: null },
            { 
              lastLowBalanceWarning: { 
                lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
              } 
            }
          ]
        },
        select: {
          id: true,
          email: true,
          name: true,
          creditBalance: true,
          lastLowBalanceWarning: true
        }
      })

      console.log(`Found ${lowBalanceUsers.length} users with low balance`)

      for (const user of lowBalanceUsers) {
        try {
          // Get personalized recommendations for the user
          const recommendations = await CreditRecommendationEngine.getRecommendations(
            user.id,
            user.creditBalance
          )

          const recommendedPackage = recommendations.length > 0 ? {
            name: recommendations[0].package.name,
            credits: recommendations[0].package.credits + recommendations[0].package.bonusCredits,
            price: recommendations[0].package.price
          } : undefined

          // Send low balance warning
          await CreditPurchaseNotificationService.sendLowBalanceWarning({
            userEmail: user.email,
            userName: user.name || 'Valued Reader',
            currentBalance: user.creditBalance,
            recommendedPackage
          })

          // Update last warning timestamp
          await prisma.user.update({
            where: { id: user.id },
            data: { lastLowBalanceWarning: new Date() }
          })

          console.log(`✅ Sent low balance warning to ${user.email}`)

        } catch (error) {
          console.error(`❌ Failed to send low balance warning to ${user.email}:`, error)
        }
      }

      console.log('✅ Weekly low balance check completed')

    } catch (error) {
      console.error('❌ Weekly low balance check failed:', error)
      throw error
    }
  }

  /**
   * Schedule purchase reminder emails for users who abandoned cart
   */
  static async schedulePurchaseReminders(): Promise<void> {
    try {
      console.log('🛒 Running purchase reminder check...')

      // Find users who started but didn't complete a purchase in the last 24 hours
      const abandonedPurchases = await prisma.creditPurchase.findMany({
        where: {
          status: 'PENDING',
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
            lt: new Date(Date.now() - 2 * 60 * 60 * 1000) // But not in last 2 hours
          }
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              emailNotifications: true
            }
          },
          package: true
        }
      })

      console.log(`Found ${abandonedPurchases.length} abandoned purchases`)

      for (const purchase of abandonedPurchases) {
        if (!purchase.user.emailNotifications) continue

        try {
          // Check if we already sent a reminder for this purchase
          const existingReminder = await prisma.emailNotification.findFirst({
            where: {
              type: 'PURCHASE_REMINDER',
              recipient: purchase.user.email,
              metadata: {
                path: ['purchaseId'],
                equals: purchase.id
              }
            }
          }).catch(() => null)

          if (existingReminder) continue

          await this.sendPurchaseReminder({
            userEmail: purchase.user.email,
            userName: purchase.user.name || 'Valued Reader',
            packageName: purchase.package.name,
            credits: purchase.credits,
            bonusCredits: purchase.bonusCredits,
            price: purchase.amount,
            purchaseId: purchase.id
          })

          console.log(`✅ Sent purchase reminder to ${purchase.user.email}`)

        } catch (error) {
          console.error(`❌ Failed to send purchase reminder to ${purchase.user.email}:`, error)
        }
      }

      console.log('✅ Purchase reminder check completed')

    } catch (error) {
      console.error('❌ Purchase reminder check failed:', error)
      throw error
    }
  }

  /**
   * Send weekly digest emails to active users
   */
  static async sendWeeklyDigest(): Promise<void> {
    try {
      console.log('📊 Sending weekly digest emails...')

      // Find active users who want email notifications
      const activeUsers = await prisma.user.findMany({
        where: {
          emailNotifications: true,
          // Users who have made a transaction in the last 30 days
          creditTransactions: {
            some: {
              createdAt: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
              }
            }
          }
        },
        select: {
          id: true,
          email: true,
          name: true,
          creditBalance: true
        },
        take: 100 // Limit to prevent overwhelming the email service
      })

      console.log(`Sending weekly digest to ${activeUsers.length} active users`)

      for (const user of activeUsers) {
        try {
          await this.sendWeeklyDigestEmail(user)
          console.log(`✅ Sent weekly digest to ${user.email}`)
        } catch (error) {
          console.error(`❌ Failed to send weekly digest to ${user.email}:`, error)
        }
      }

      console.log('✅ Weekly digest emails completed')

    } catch (error) {
      console.error('❌ Weekly digest failed:', error)
      throw error
    }
  }

  /**
   * Send purchase reminder email
   */
  private static async sendPurchaseReminder(data: {
    userEmail: string
    userName: string
    packageName: string
    credits: number
    bonusCredits: number
    price: number
    purchaseId: string
  }): Promise<void> {
    const subject = `Complete Your Credit Purchase - ${data.packageName}`
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Complete Your Purchase</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #F59E0B; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .highlight { background: #FEF3C7; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .button { background: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🛒 Complete Your Purchase</h1>
          </div>
          
          <div class="content">
            <h2>Hi ${data.userName}!</h2>
            
            <p>You started purchasing credits but didn't complete the process. Don't miss out on your credits!</p>
            
            <div class="highlight">
              <h3>Your Selected Package:</h3>
              <p><strong>${data.packageName}</strong></p>
              <p>Credits: ${data.credits}${data.bonusCredits > 0 ? ` + ${data.bonusCredits} bonus` : ''}</p>
              <p>Price: $${data.price.toFixed(2)}</p>
            </div>
            
            <p>Complete your purchase now and start enjoying premium content!</p>
            
            <p style="text-align: center;">
              <a href="${process.env.NEXTAUTH_URL}/dashboard/credits?purchase=${data.purchaseId}" class="button">
                Complete Purchase
              </a>
            </p>
          </div>
          
          <div class="footer">
            <p>This link will expire in 24 hours.</p>
            <p>© ${new Date().getFullYear()} Black Blogs. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `

    const text = `
Complete Your Credit Purchase

Hi ${data.userName}!

You started purchasing credits but didn't complete the process. Don't miss out on your credits!

Your Selected Package: ${data.packageName}
Credits: ${data.credits}${data.bonusCredits > 0 ? ` + ${data.bonusCredits} bonus` : ''}
Price: $${data.price.toFixed(2)}

Complete your purchase: ${process.env.NEXTAUTH_URL}/dashboard/credits?purchase=${data.purchaseId}

This link will expire in 24 hours.
    `.trim()

    // Import EmailService dynamically to avoid circular dependencies
    const { EmailService } = await import('@/lib/email/email-service')
    
    await EmailService.sendEmail({
      to: data.userEmail,
      subject,
      html,
      text
    })
  }

  /**
   * Send weekly digest email
   */
  private static async sendWeeklyDigestEmail(user: {
    id: string
    email: string
    name: string | null
    creditBalance: number
  }): Promise<void> {
    // Get user's reading stats for the week
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    
    const weeklyStats = await prisma.creditTransaction.aggregate({
      where: {
        userId: user.id,
        type: 'DEBIT',
        createdAt: { gte: weekAgo }
      },
      _sum: { amount: true },
      _count: true
    })

    const creditsSpent = Math.abs(weeklyStats._sum.amount || 0)
    const chaptersRead = weeklyStats._count

    const subject = `Your Weekly Reading Summary - ${chaptersRead} chapters read!`
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Weekly Reading Summary</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #4F46E5; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .stats { background: #EEF2FF; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .button { background: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📚 Your Weekly Reading Summary</h1>
          </div>
          
          <div class="content">
            <h2>Hi ${user.name || 'Reader'}!</h2>
            
            <p>Here's your reading activity for this week:</p>
            
            <div class="stats">
              <h3>This Week's Stats:</h3>
              <p><strong>Chapters Read:</strong> ${chaptersRead}</p>
              <p><strong>Credits Spent:</strong> ${creditsSpent}</p>
              <p><strong>Current Balance:</strong> ${user.creditBalance} credits</p>
            </div>
            
            ${user.creditBalance < 50 ? `
              <p>Your credit balance is getting low. Consider topping up to continue your reading journey!</p>
              <p style="text-align: center;">
                <a href="${process.env.NEXTAUTH_URL}/dashboard/credits" class="button">
                  Top Up Credits
                </a>
              </p>
            ` : ''}
            
            <p>Keep reading and supporting your favorite authors!</p>
          </div>
          
          <div class="footer">
            <p>You can unsubscribe from these emails in your account settings.</p>
            <p>© ${new Date().getFullYear()} Black Blogs. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `

    // Import EmailService dynamically
    const { EmailService } = await import('@/lib/email/email-service')
    
    await EmailService.sendEmail({
      to: user.email,
      subject,
      html
    })
  }

  /**
   * Run all scheduled email jobs
   */
  static async runScheduledJobs(): Promise<void> {
    console.log('🚀 Running scheduled email jobs...')
    
    try {
      await Promise.all([
        this.scheduleWeeklyLowBalanceCheck(),
        this.schedulePurchaseReminders()
      ])
      
      console.log('✅ All scheduled email jobs completed')
    } catch (error) {
      console.error('❌ Scheduled email jobs failed:', error)
      throw error
    }
  }
}
